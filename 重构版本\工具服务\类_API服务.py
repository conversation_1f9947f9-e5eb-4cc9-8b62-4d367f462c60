# -*- coding: utf-8 -*-
"""
API服务类
负责统一管理交易所API接口功能，封装原有的API接口类
"""

import logging
import sys
import os
from typing import Optional, Dict, Any, List

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)


class 类_API服务:
    """
    API服务类
    封装原有的交易所API接口功能，提供服务化接口
    """
    
    def __init__(self):
        """
        初始化API服务
        
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 设置基本属性
        self.类_模块名 = "API服务"
        
        # 步骤2: 初始化API接口字典
        self._API接口字典: Dict[str, Any] = {}
        
        # 步骤3: 初始化支持的交易所列表
        self.支持的交易所列表 = ["OKX", "币安"]
        
        # 步骤4: 初始化API接口
        self.初始化API接口()
        
        logger.info("API服务初始化完成")
        print("API服务初始化完成")
    
    def 初始化API接口(self) -> None:
        """
        初始化所有支持的API接口
        
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        try:
            # 步骤1: 添加项目根目录到sys.path
            当前目录 = os.path.dirname(os.path.abspath(__file__))
            项目根目录 = os.path.dirname(os.path.dirname(当前目录))
            if 项目根目录 not in sys.path:
                sys.path.insert(0, 项目根目录)
            
            # 步骤2: 初始化OKX接口
            self.初始化OKX接口()
            
            # 步骤3: 初始化币安接口（如果需要）
            # self.初始化币安接口()
            
            logger.info("API接口初始化完成")
            
        except Exception as e:
            logger.error(f"初始化API接口失败: {e}", exc_info=True)
            raise
    
    def 初始化OKX接口(self) -> None:
        """
        初始化OKX交易所API接口
        
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        try:
            # 步骤1: 导入OKX接口类
            from 模块类.交易所API接口.OKX.类_OKX接口 import 类_OKX接口
            
            # 步骤2: 创建OKX接口实例
            self._API接口字典["OKX"] = 类_OKX接口()
            
            logger.info("OKX API接口初始化完成")
            
        except ImportError as e:
            logger.error(f"导入OKX接口类失败: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"初始化OKX接口失败: {e}", exc_info=True)
            raise
    
    def 获取API接口(self, 参_交易所名称: str):
        """
        获取指定交易所的API接口实例
        
        @param 参_交易所名称 (str): 交易所名称
        @return: API接口实例
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            if 参_交易所名称 in self._API接口字典:
                return self._API接口字典[参_交易所名称]
            else:
                logger.error(f"不支持的交易所: {参_交易所名称}")
                return None
        except Exception as e:
            logger.error(f"获取API接口失败: {参_交易所名称}, 错误: {e}", exc_info=True)
            return None
    
    def 获取OKX接口(self):
        """
        获取OKX交易所API接口
        
        @return: OKX API接口实例
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            return self.获取API接口("OKX")
        except Exception as e:
            logger.error(f"获取OKX接口失败: {e}", exc_info=True)
            return None
    
    def 设置API密钥(self, 参_交易所名称: str, 参_API密钥: str, 参_密钥: str, 参_密码短语: str = "") -> bool:
        """
        设置指定交易所的API密钥
        
        @param 参_交易所名称 (str): 交易所名称
        @param 参_API密钥 (str): API Key
        @param 参_密钥 (str): Secret Key
        @param 参_密码短语 (str): Passphrase（OKX需要）
        @return bool: 设置是否成功
        @exception Exception: 设置过程中可能发生的未知错误
        """
        try:
            API接口 = self.获取API接口(参_交易所名称)
            if API接口 is None:
                logger.error(f"无法获取{参_交易所名称}的API接口")
                return False
            
            # 调用API接口的设置密钥方法
            if hasattr(API接口, '设置API密钥'):
                成功 = API接口.设置API密钥(参_API密钥, 参_密钥, 参_密码短语)
                if 成功:
                    logger.info(f"{参_交易所名称} API密钥设置成功")
                else:
                    logger.error(f"{参_交易所名称} API密钥设置失败")
                return 成功
            else:
                logger.error(f"{参_交易所名称} API接口不支持设置密钥")
                return False
                
        except Exception as e:
            logger.error(f"设置{参_交易所名称} API密钥失败: {e}", exc_info=True)
            return False
    
    def 获取账户余额(self, 参_交易所名称: str) -> Optional[Dict[str, Any]]:
        """
        获取指定交易所的账户余额
        
        @param 参_交易所名称 (str): 交易所名称
        @return Optional[Dict[str, Any]]: 账户余额信息，失败返回None
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            API接口 = self.获取API接口(参_交易所名称)
            if API接口 is None:
                logger.error(f"无法获取{参_交易所名称}的API接口")
                return None
            
            # 调用API接口的获取余额方法
            if hasattr(API接口, '获取账户余额'):
                余额信息 = API接口.获取账户余额()
                if 余额信息:
                    logger.debug(f"获取{参_交易所名称}账户余额成功")
                else:
                    logger.error(f"获取{参_交易所名称}账户余额失败")
                return 余额信息
            else:
                logger.error(f"{参_交易所名称} API接口不支持获取余额")
                return None
                
        except Exception as e:
            logger.error(f"获取{参_交易所名称}账户余额失败: {e}", exc_info=True)
            return None
    
    def 获取交易对信息(self, 参_交易所名称: str, 参_交易对: str) -> Optional[Dict[str, Any]]:
        """
        获取指定交易对的信息
        
        @param 参_交易所名称 (str): 交易所名称
        @param 参_交易对 (str): 交易对名称
        @return Optional[Dict[str, Any]]: 交易对信息，失败返回None
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            API接口 = self.获取API接口(参_交易所名称)
            if API接口 is None:
                logger.error(f"无法获取{参_交易所名称}的API接口")
                return None
            
            # 调用API接口的获取交易对信息方法
            if hasattr(API接口, '获取交易对信息'):
                交易对信息 = API接口.获取交易对信息(参_交易对)
                if 交易对信息:
                    logger.debug(f"获取{参_交易所名称}交易对{参_交易对}信息成功")
                else:
                    logger.error(f"获取{参_交易所名称}交易对{参_交易对}信息失败")
                return 交易对信息
            else:
                logger.error(f"{参_交易所名称} API接口不支持获取交易对信息")
                return None
                
        except Exception as e:
            logger.error(f"获取{参_交易所名称}交易对{参_交易对}信息失败: {e}", exc_info=True)
            return None
    
    def 下单(self, 参_交易所名称: str, 参_交易对: str, 参_方向: str, 参_数量: float, 参_价格: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """
        在指定交易所下单
        
        @param 参_交易所名称 (str): 交易所名称
        @param 参_交易对 (str): 交易对名称
        @param 参_方向 (str): 交易方向（buy/sell）
        @param 参_数量 (float): 交易数量
        @param 参_价格 (Optional[float]): 交易价格，None表示市价单
        @return Optional[Dict[str, Any]]: 下单结果，失败返回None
        @exception Exception: 下单过程中可能发生的未知错误
        """
        try:
            API接口 = self.获取API接口(参_交易所名称)
            if API接口 is None:
                logger.error(f"无法获取{参_交易所名称}的API接口")
                return None
            
            # 调用API接口的下单方法
            if hasattr(API接口, '下单'):
                下单结果 = API接口.下单(参_交易对, 参_方向, 参_数量, 参_价格)
                if 下单结果:
                    logger.info(f"{参_交易所名称}下单成功: {参_交易对} {参_方向} {参_数量}")
                else:
                    logger.error(f"{参_交易所名称}下单失败: {参_交易对} {参_方向} {参_数量}")
                return 下单结果
            else:
                logger.error(f"{参_交易所名称} API接口不支持下单")
                return None
                
        except Exception as e:
            logger.error(f"{参_交易所名称}下单失败: {e}", exc_info=True)
            return None
    
    def 获取支持的交易所列表(self) -> List[str]:
        """
        获取支持的交易所列表
        
        @return List[str]: 支持的交易所名称列表
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            return self.支持的交易所列表.copy()
        except Exception as e:
            logger.error(f"获取支持的交易所列表失败: {e}", exc_info=True)
            return []
    
    def 是否支持交易所(self, 参_交易所名称: str) -> bool:
        """
        检查是否支持指定交易所
        
        @param 参_交易所名称 (str): 交易所名称
        @return bool: 是否支持
        @exception Exception: 检查过程中可能发生的未知错误
        """
        try:
            return 参_交易所名称 in self.支持的交易所列表
        except Exception as e:
            logger.error(f"检查交易所支持性失败: {e}", exc_info=True)
            return False
