<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>StrategyEditWindow</class>
 <widget class="QDialog" name="StrategyEditWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>700</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>修改策略</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_main">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>15</number>
   </property>
   <property name="topMargin">
    <number>15</number>
   </property>
   <property name="rightMargin">
    <number>15</number>
   </property>
   <property name="bottomMargin">
    <number>15</number>
   </property>
   <item>
    <widget class="QGroupBox" name="groupBox_current_strategy_info">
     <property name="title">
      <string>当前策略信息</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_strategy_info">
      <property name="leftMargin">
       <number>10</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>10</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <property name="spacing">
       <number>8</number>
      </property>
      <item row="0" column="0">
       <widget class="QLabel" name="label_trading_pair">
        <property name="font">
         <font>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>交易对:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLabel" name="label_trading_pair_value">
        <property name="text">
         <string>BTC/USDT</string>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <spacer name="horizontalSpacer_info_1">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_strategy_type">
        <property name="font">
         <font>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>策略类型:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLabel" name="label_strategy_type_value">
        <property name="text">
         <string>自定义策略</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_create_time">
        <property name="font">
         <font>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>创建时间:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLabel" name="label_create_time_value">
        <property name="text">
         <string>2024-01-01 10:00:00</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_current_status">
        <property name="font">
         <font>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>当前状态:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QLabel" name="label_current_status_value">
        <property name="styleSheet">
         <string>color: green; font-weight: bold;</string>
        </property>
        <property name="text">
         <string>运行中</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_edit_options">
     <property name="title">
      <string>修改选项</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_edit_options">
      <property name="spacing">
       <number>20</number>
      </property>
      <property name="leftMargin">
       <number>10</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>10</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <widget class="QRadioButton" name="radioButton_edit_parameters">
        <property name="text">
         <string>修改自定义策略</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="radioButton_change_strategy">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="text">
         <string>修改AI策略</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_edit_options">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QStackedWidget" name="stackedWidget_edit_content">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="page_edit_parameters">
      <layout class="QVBoxLayout" name="verticalLayout_edit_parameters">
       <property name="spacing">
        <number>8</number>
       </property>
       <item>
        <widget class="QScrollArea" name="scrollArea_parameters">
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <widget class="QWidget" name="scrollAreaWidgetContents_parameters">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>-143</y>
            <width>638</width>
            <height>457</height>
           </rect>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_parameters_content">
           <property name="spacing">
            <number>8</number>
           </property>
           <property name="leftMargin">
            <number>10</number>
           </property>
           <property name="topMargin">
            <number>10</number>
           </property>
           <property name="rightMargin">
            <number>10</number>
           </property>
           <property name="bottomMargin">
            <number>10</number>
           </property>
           <item>
            <widget class="QGroupBox" name="groupBox_edit_first_amount">
             <property name="title">
              <string>首单金额</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_edit_first_amount">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_edit_first_amount">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> U</string>
                </property>
                <property name="maximum">
                 <double>99999.000000000000000</double>
                </property>
                <property name="value">
                 <double>10.000000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_edit_first_amount">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_edit_drop_percent">
             <property name="title">
              <string>跌幅多少加仓</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_edit_drop_percent">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_edit_drop_percent">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> %</string>
                </property>
                <property name="maximum">
                 <double>100.000000000000000</double>
                </property>
                <property name="value">
                 <double>1.000000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_edit_drop_percent">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_edit_position_multiplier">
             <property name="title">
              <string>加仓倍数</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_edit_position_multiplier">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_edit_position_multiplier">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> 倍</string>
                </property>
                <property name="minimum">
                 <double>1.000000000000000</double>
                </property>
                <property name="maximum">
                 <double>10.000000000000000</double>
                </property>
                <property name="value">
                 <double>1.300000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_edit_position_multiplier">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_edit_profit_percent">
             <property name="title">
              <string>百分比止盈比例</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_edit_profit_percent">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_edit_profit_percent">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> %</string>
                </property>
                <property name="maximum">
                 <double>100.000000000000000</double>
                </property>
                <property name="value">
                 <double>1.500000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_edit_profit_percent">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_edit_max_orders">
             <property name="title">
              <string>最大持仓单数</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_edit_max_orders">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QSpinBox" name="spinBox_edit_max_orders">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> 次</string>
                </property>
                <property name="minimum">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <number>100</number>
                </property>
                <property name="value">
                 <number>10</number>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_edit_max_orders">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_edit_profit_stop">
             <property name="title">
              <string>盈利多少停止机器人</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_edit_profit_stop">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_edit_profit_stop">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> U</string>
                </property>
                <property name="maximum">
                 <double>99999.000000000000000</double>
                </property>
                <property name="value">
                 <double>100.000000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_edit_profit_stop">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_edit_dynamic_position">
             <property name="title">
              <string>动态持仓</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_edit_dynamic_position">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QCheckBox" name="checkBox_edit_dynamic_position">
                <property name="text">
                 <string>开启动态持仓</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_edit_dynamic_position">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_edit_rebound_percent">
             <property name="title">
              <string>百分比反弹多少补仓</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_edit_rebound_percent">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_edit_rebound_percent">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> %</string>
                </property>
                <property name="maximum">
                 <double>100.000000000000000</double>
                </property>
                <property name="value">
                 <double>0.300000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_edit_rebound_percent">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="page_change_strategy">
      <layout class="QVBoxLayout" name="verticalLayout_change_strategy">
       <property name="spacing">
        <number>8</number>
       </property>
       <item>
        <widget class="QGroupBox" name="groupBox_change_strategy_type">
         <property name="title">
          <string>策略类型选择</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_change_strategy_type">
          <property name="spacing">
           <number>20</number>
          </property>
          <property name="leftMargin">
           <number>10</number>
          </property>
          <property name="topMargin">
           <number>10</number>
          </property>
          <property name="rightMargin">
           <number>10</number>
          </property>
          <property name="bottomMargin">
           <number>10</number>
          </property>
          <item>
           <widget class="QRadioButton" name="radioButton_change_aggressive">
            <property name="text">
             <string>进攻型策略</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="radioButton_change_defensive">
            <property name="text">
             <string>防守型策略</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="radioButton_change_universal">
            <property name="text">
             <string>通用型策略</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_change_strategy_type">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_change_sub_strategy">
         <property name="title">
          <string>子策略选择</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_change_sub_strategy">
          <property name="spacing">
           <number>6</number>
          </property>
          <property name="leftMargin">
           <number>10</number>
          </property>
          <property name="topMargin">
           <number>10</number>
          </property>
          <property name="rightMargin">
           <number>10</number>
          </property>
          <property name="bottomMargin">
           <number>10</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_change_sub_strategies">
            <property name="spacing">
             <number>15</number>
            </property>
            <item>
             <widget class="QRadioButton" name="radioButton_change_universal_a">
              <property name="visible">
               <bool>false</bool>
              </property>
              <property name="text">
               <string>A</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_change_crazy">
              <property name="text">
               <string>疯狂型</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_change_aggressive_sub">
              <property name="text">
               <string>激进型</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_change_balanced">
              <property name="text">
               <string>平衡型</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_change_stable">
              <property name="text">
               <string>稳健型</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_change_conservative">
              <property name="text">
               <string>保守型</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_change_sub_strategies">
              <property name="orientation">
               <enum>Qt::Orientation::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_change_capital">
         <property name="title">
          <string>投入本金设置</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_change_capital">
          <property name="spacing">
           <number>6</number>
          </property>
          <property name="leftMargin">
           <number>10</number>
          </property>
          <property name="topMargin">
           <number>10</number>
          </property>
          <property name="rightMargin">
           <number>10</number>
          </property>
          <property name="bottomMargin">
           <number>10</number>
          </property>
          <item>
           <widget class="QLabel" name="label_change_capital">
            <property name="text">
             <string>投入本金:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDoubleSpinBox" name="doubleSpinBox_change_capital">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="suffix">
             <string> U</string>
            </property>
            <property name="maximum">
             <double>99999.000000000000000</double>
            </property>
            <property name="value">
             <double>100.000000000000000</double>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_change_capital">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_change_strategy">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::StandardButton::Cancel|QDialogButtonBox::StandardButton::Save</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>StrategyEditWindow</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>StrategyEditWindow</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
