# -*- coding: utf-8 -*-
"""
计算服务类
负责统一管理核心计算功能，封装原有的核心计算类
"""

import logging
import sys
import os
from typing import Optional, Dict, Any, List

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)


class 类_计算服务:
    """
    计算服务类
    封装原有的核心计算功能，提供服务化接口
    """
    
    def __init__(self, 参_数据库服务, 参_API服务):
        """
        初始化计算服务
        
        @param 参_数据库服务: 数据库服务实例
        @param 参_API服务: API服务实例
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 设置基本属性
        self.类_模块名 = "计算服务"
        self.数据库服务 = 参_数据库服务
        self.API服务 = 参_API服务
        
        # 步骤2: 初始化核心计算器
        self._核心计算器 = None
        self.初始化核心计算器()
        
        logger.info("计算服务初始化完成")
        print("计算服务初始化完成")
    
    def 初始化核心计算器(self) -> None:
        """
        初始化核心计算器
        
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        try:
            # 步骤1: 添加项目根目录到sys.path
            当前目录 = os.path.dirname(os.path.abspath(__file__))
            项目根目录 = os.path.dirname(os.path.dirname(当前目录))
            if 项目根目录 not in sys.path:
                sys.path.insert(0, 项目根目录)
            
            # 步骤2: 导入原有的核心计算类
            from 模块类.功能类.类_核心计算 import 类_核心计算
            
            # 步骤3: 创建核心计算器实例
            # 传入数据库管理器和API接口
            数据库管理器 = self.数据库服务.获取数据库管理器()
            OKX接口 = self.API服务.获取OKX接口()
            
            self._核心计算器 = 类_核心计算(数据库管理器, OKX接口)
            
            logger.info("核心计算器初始化完成")
            
        except ImportError as e:
            logger.error(f"导入核心计算类失败: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"初始化核心计算器失败: {e}", exc_info=True)
            raise
    
    def 获取核心计算器(self):
        """
        获取原有的核心计算器实例
        
        @return: 核心计算器实例
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            if self._核心计算器 is None:
                self.初始化核心计算器()
            return self._核心计算器
        except Exception as e:
            logger.error(f"获取核心计算器失败: {e}", exc_info=True)
            raise
    
    def 计算交易数量(self, 参_机器人编号: str, 参_交易对: str, 参_当前价格: float) -> Optional[float]:
        """
        计算交易数量
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_交易对 (str): 交易对名称
        @param 参_当前价格 (float): 当前价格
        @return Optional[float]: 计算的交易数量，失败返回None
        @exception Exception: 计算过程中可能发生的未知错误
        """
        try:
            核心计算器 = self.获取核心计算器()
            
            if hasattr(核心计算器, '计算交易数量'):
                交易数量 = 核心计算器.计算交易数量(参_机器人编号, 参_交易对, 参_当前价格)
                
                if 交易数量 is not None:
                    logger.debug(f"计算交易数量成功: 机器人{参_机器人编号}, 数量{交易数量}")
                else:
                    logger.error(f"计算交易数量失败: 机器人{参_机器人编号}")
                
                return 交易数量
            else:
                logger.error("核心计算器不支持计算交易数量")
                return None
                
        except Exception as e:
            logger.error(f"计算交易数量失败: 机器人{参_机器人编号}, 错误: {e}", exc_info=True)
            return None
    
    def 计算止损价格(self, 参_机器人编号: str, 参_开仓价格: float, 参_交易方向: str) -> Optional[float]:
        """
        计算止损价格
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_开仓价格 (float): 开仓价格
        @param 参_交易方向 (str): 交易方向（long/short）
        @return Optional[float]: 计算的止损价格，失败返回None
        @exception Exception: 计算过程中可能发生的未知错误
        """
        try:
            核心计算器 = self.获取核心计算器()
            
            if hasattr(核心计算器, '计算止损价格'):
                止损价格 = 核心计算器.计算止损价格(参_机器人编号, 参_开仓价格, 参_交易方向)
                
                if 止损价格 is not None:
                    logger.debug(f"计算止损价格成功: 机器人{参_机器人编号}, 止损价格{止损价格}")
                else:
                    logger.error(f"计算止损价格失败: 机器人{参_机器人编号}")
                
                return 止损价格
            else:
                logger.error("核心计算器不支持计算止损价格")
                return None
                
        except Exception as e:
            logger.error(f"计算止损价格失败: 机器人{参_机器人编号}, 错误: {e}", exc_info=True)
            return None
    
    def 计算止盈价格(self, 参_机器人编号: str, 参_开仓价格: float, 参_交易方向: str) -> Optional[float]:
        """
        计算止盈价格
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_开仓价格 (float): 开仓价格
        @param 参_交易方向 (str): 交易方向（long/short）
        @return Optional[float]: 计算的止盈价格，失败返回None
        @exception Exception: 计算过程中可能发生的未知错误
        """
        try:
            核心计算器 = self.获取核心计算器()
            
            if hasattr(核心计算器, '计算止盈价格'):
                止盈价格 = 核心计算器.计算止盈价格(参_机器人编号, 参_开仓价格, 参_交易方向)
                
                if 止盈价格 is not None:
                    logger.debug(f"计算止盈价格成功: 机器人{参_机器人编号}, 止盈价格{止盈价格}")
                else:
                    logger.error(f"计算止盈价格失败: 机器人{参_机器人编号}")
                
                return 止盈价格
            else:
                logger.error("核心计算器不支持计算止盈价格")
                return None
                
        except Exception as e:
            logger.error(f"计算止盈价格失败: 机器人{参_机器人编号}, 错误: {e}", exc_info=True)
            return None
    
    def 计算盈亏(self, 参_开仓价格: float, 参_当前价格: float, 参_数量: float, 参_交易方向: str) -> Optional[float]:
        """
        计算盈亏
        
        @param 参_开仓价格 (float): 开仓价格
        @param 参_当前价格 (float): 当前价格
        @param 参_数量 (float): 交易数量
        @param 参_交易方向 (str): 交易方向（long/short）
        @return Optional[float]: 计算的盈亏金额，失败返回None
        @exception Exception: 计算过程中可能发生的未知错误
        """
        try:
            核心计算器 = self.获取核心计算器()
            
            if hasattr(核心计算器, '计算盈亏'):
                盈亏金额 = 核心计算器.计算盈亏(参_开仓价格, 参_当前价格, 参_数量, 参_交易方向)
                
                if 盈亏金额 is not None:
                    logger.debug(f"计算盈亏成功: 盈亏{盈亏金额}")
                else:
                    logger.error("计算盈亏失败")
                
                return 盈亏金额
            else:
                logger.error("核心计算器不支持计算盈亏")
                return None
                
        except Exception as e:
            logger.error(f"计算盈亏失败: {e}", exc_info=True)
            return None
    
    def 计算手续费(self, 参_交易金额: float, 参_手续费率: float) -> Optional[float]:
        """
        计算手续费
        
        @param 参_交易金额 (float): 交易金额
        @param 参_手续费率 (float): 手续费率
        @return Optional[float]: 计算的手续费，失败返回None
        @exception Exception: 计算过程中可能发生的未知错误
        """
        try:
            核心计算器 = self.获取核心计算器()
            
            if hasattr(核心计算器, '计算手续费'):
                手续费 = 核心计算器.计算手续费(参_交易金额, 参_手续费率)
                
                if 手续费 is not None:
                    logger.debug(f"计算手续费成功: 手续费{手续费}")
                else:
                    logger.error("计算手续费失败")
                
                return 手续费
            else:
                # 如果核心计算器不支持，使用简单计算
                手续费 = 参_交易金额 * 参_手续费率
                logger.debug(f"使用简单方法计算手续费: {手续费}")
                return 手续费
                
        except Exception as e:
            logger.error(f"计算手续费失败: {e}", exc_info=True)
            return None
    
    def 验证交易参数(self, 参_机器人编号: str, 参_交易对: str, 参_数量: float, 参_价格: float) -> bool:
        """
        验证交易参数是否有效
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_交易对 (str): 交易对名称
        @param 参_数量 (float): 交易数量
        @param 参_价格 (float): 交易价格
        @return bool: 参数是否有效
        @exception Exception: 验证过程中可能发生的未知错误
        """
        try:
            核心计算器 = self.获取核心计算器()
            
            if hasattr(核心计算器, '验证交易参数'):
                验证结果 = 核心计算器.验证交易参数(参_机器人编号, 参_交易对, 参_数量, 参_价格)
                
                if 验证结果:
                    logger.debug(f"交易参数验证通过: 机器人{参_机器人编号}")
                else:
                    logger.warning(f"交易参数验证失败: 机器人{参_机器人编号}")
                
                return 验证结果
            else:
                # 如果核心计算器不支持，进行基本验证
                if 参_数量 <= 0 or 参_价格 <= 0:
                    logger.warning("交易数量或价格无效")
                    return False
                
                logger.debug("使用基本方法验证交易参数通过")
                return True
                
        except Exception as e:
            logger.error(f"验证交易参数失败: {e}", exc_info=True)
            return False
    
    def 是否可用(self) -> bool:
        """
        检查计算服务是否可用
        
        @return bool: 计算服务是否可用
        @exception Exception: 检查过程中可能发生的未知错误
        """
        try:
            return self._核心计算器 is not None
        except Exception as e:
            logger.error(f"检查计算服务可用性失败: {e}", exc_info=True)
            return False
