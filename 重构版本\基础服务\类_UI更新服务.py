# -*- coding: utf-8 -*-
"""
UI更新服务类
负责统一管理线程安全的UI更新功能，封装原有的线程安全UI更新器
"""

import logging
import sys
import os
from typing import Optional, Dict, Any
from PySide6.QtCore import QObject, Signal, Slot

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)


class 类_UI更新服务(QObject):
    """
    UI更新服务类
    封装原有的线程安全UI更新器，提供服务化接口
    继承QObject以支持信号槽机制
    """
    
    # 定义信号，用于跨线程通信
    信号_更新单个单元格 = Signal(str, str, object)  # 机器人编号, 字段名, 新值
    信号_更新多个单元格 = Signal(str, dict)  # 机器人编号, 字段字典
    信号_刷新表格数据 = Signal()  # 刷新整个表格
    信号_显示状态消息 = Signal(str, int)  # 消息内容, 超时时间(毫秒)
    
    def __init__(self):
        """
        初始化UI更新服务
        
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        super().__init__()
        
        # 步骤1: 设置基本属性
        self.类_模块名 = "UI更新服务"
        
        # 步骤2: 初始化主窗口引用
        self._主窗口 = None
        
        # 步骤3: 初始化原有的UI更新器
        self._原有UI更新器 = None
        
        logger.info("UI更新服务初始化完成")
        print("UI更新服务初始化完成")
    
    def 设置主窗口(self, 参_主窗口) -> None:
        """
        设置主窗口引用
        在主窗口创建后调用此方法
        
        @param 参_主窗口: 主窗口实例
        @return None
        @exception Exception: 设置过程中可能发生的未知错误
        """
        try:
            # 步骤1: 保存主窗口引用
            self._主窗口 = 参_主窗口
            
            # 步骤2: 创建原有的UI更新器
            self.创建原有UI更新器()
            
            # 步骤3: 连接信号槽
            self.连接信号槽()
            
            logger.info("主窗口设置完成，UI更新服务已激活")
            
        except Exception as e:
            logger.error(f"设置主窗口失败: {e}", exc_info=True)
            raise
    
    def 创建原有UI更新器(self) -> None:
        """
        创建原有的UI更新器实例
        
        @return None
        @exception Exception: 创建过程中可能发生的未知错误
        """
        try:
            # 步骤1: 添加项目根目录到sys.path
            当前目录 = os.path.dirname(os.path.abspath(__file__))
            项目根目录 = os.path.dirname(os.path.dirname(当前目录))
            if 项目根目录 not in sys.path:
                sys.path.insert(0, 项目根目录)
            
            # 步骤2: 导入原有的UI更新器类
            from 窗口UI布局.UI管理.主窗口模块.类_线程安全UI更新器 import 类_线程安全UI更新器
            
            # 步骤3: 创建UI更新器实例
            if self._主窗口:
                self._原有UI更新器 = 类_线程安全UI更新器(self._主窗口)
                logger.info("原有UI更新器创建完成")
            else:
                logger.warning("主窗口未设置，无法创建UI更新器")
            
        except ImportError as e:
            logger.error(f"导入UI更新器类失败: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"创建原有UI更新器失败: {e}", exc_info=True)
            raise
    
    def 连接信号槽(self) -> None:
        """
        连接信号槽
        将服务的信号连接到原有UI更新器的方法
        
        @return None
        @exception Exception: 连接过程中可能发生的未知错误
        """
        try:
            if self._原有UI更新器:
                # 连接信号到原有UI更新器的方法
                self.信号_更新单个单元格.connect(self._原有UI更新器.请求更新单个单元格)
                self.信号_更新多个单元格.connect(self._原有UI更新器.请求更新多个单元格)
                self.信号_刷新表格数据.connect(self._原有UI更新器.请求刷新表格数据)
                self.信号_显示状态消息.connect(self._原有UI更新器.请求显示状态消息)
                
                logger.info("UI更新服务信号槽连接完成")
            else:
                logger.warning("原有UI更新器未创建，无法连接信号槽")
                
        except Exception as e:
            logger.error(f"连接信号槽失败: {e}", exc_info=True)
            raise
    
    # ========== 线程安全的公共接口方法 ==========
    # 这些方法可以在任何线程中安全调用
    
    def 请求更新单个单元格(self, 参_机器人编号: str, 参_字段名: str, 参_新值) -> None:
        """
        请求更新单个单元格（线程安全）
        
        @param 参_机器人编号 (str): 要更新的机器人编号
        @param 参_字段名 (str): 要更新的字段名
        @param 参_新值: 新的值
        @return None
        @exception Exception: 发送信号过程中可能发生的未知错误
        """
        try:
            # 步骤1: 验证输入参数
            if not 参_机器人编号 or not 参_字段名:
                logger.warning(f"[{self.类_模块名}] 机器人编号或字段名为空")
                return
            
            # 步骤2: 发送更新信号
            self.信号_更新单个单元格.emit(参_机器人编号, 参_字段名, 参_新值)
            
            logger.debug(f"[{self.类_模块名}] 发送单元格更新请求: 机器人{参_机器人编号}, 字段{参_字段名}")
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 请求更新单个单元格时发生错误: {e}", exc_info=True)
    
    def 请求更新多个单元格(self, 参_机器人编号: str, 参_字段字典: dict) -> None:
        """
        请求批量更新多个单元格（线程安全）
        
        @param 参_机器人编号 (str): 要更新的机器人编号
        @param 参_字段字典 (dict): 字段名和新值的字典
        @return None
        @exception Exception: 发送信号过程中可能发生的未知错误
        """
        try:
            # 步骤1: 验证输入参数
            if not 参_机器人编号 or not 参_字段字典:
                logger.warning(f"[{self.类_模块名}] 机器人编号或字段字典为空")
                return
            
            # 步骤2: 发送更新信号
            self.信号_更新多个单元格.emit(参_机器人编号, 参_字段字典)
            
            logger.debug(f"[{self.类_模块名}] 发送多单元格更新请求: 机器人{参_机器人编号}, 字段数{len(参_字段字典)}")
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 请求更新多个单元格时发生错误: {e}", exc_info=True)
    
    def 请求刷新表格数据(self) -> None:
        """
        请求刷新整个表格数据（线程安全）
        
        @return None
        @exception Exception: 发送信号过程中可能发生的未知错误
        """
        try:
            # 发送刷新表格信号
            self.信号_刷新表格数据.emit()
            logger.debug(f"[{self.类_模块名}] 发送表格刷新请求")
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 请求刷新表格数据时发生错误: {e}", exc_info=True)
    
    def 请求显示状态消息(self, 参_消息: str, 参_超时毫秒: int = 5000) -> None:
        """
        请求显示状态栏消息（线程安全）
        
        @param 参_消息 (str): 要显示的消息内容
        @param 参_超时毫秒 (int): 消息显示时间，默认5秒
        @return None
        @exception Exception: 发送信号过程中可能发生的未知错误
        """
        try:
            # 发送状态消息信号
            self.信号_显示状态消息.emit(参_消息, 参_超时毫秒)
            logger.debug(f"[{self.类_模块名}] 发送状态消息请求: {参_消息}")
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 请求显示状态消息时发生错误: {e}", exc_info=True)
    
    def 获取原有UI更新器(self):
        """
        获取原有的UI更新器实例
        主要用于向后兼容
        
        @return: 原有UI更新器实例
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            return self._原有UI更新器
        except Exception as e:
            logger.error(f"获取原有UI更新器失败: {e}", exc_info=True)
            return None
    
    def 是否已激活(self) -> bool:
        """
        检查UI更新服务是否已激活
        
        @return bool: 是否已激活
        @exception Exception: 检查过程中可能发生的未知错误
        """
        try:
            return self._主窗口 is not None and self._原有UI更新器 is not None
        except Exception as e:
            logger.error(f"检查UI更新服务激活状态失败: {e}", exc_info=True)
            return False
