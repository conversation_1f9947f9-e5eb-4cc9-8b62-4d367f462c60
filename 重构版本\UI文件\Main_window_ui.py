# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'Main_window.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>urs<PERSON>,
    <PERSON><PERSON><PERSON>, QFontData<PERSON>, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QApplication, QGraphicsView, QGridLayout,
    QGroupBox, QHBoxLayout, QHeaderView, QLabel,
    QLineEdit, QMainWindow, QMenuBar, QPushButton,
    QRadioButton, QSizePolicy, QStatusBar, QTabWidget,
    QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.setEnabled(True)
        MainWindow.resize(1440, 770)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout_centralwidget = QVBoxLayout(self.centralwidget)
        self.verticalLayout_centralwidget.setObjectName(u"verticalLayout_centralwidget")
        self.tabWidget_main = QTabWidget(self.centralwidget)
        self.tabWidget_main.setObjectName(u"tabWidget_main")
        self.tabWidget_main.setMinimumSize(QSize(0, 0))
        self.tab_Control = QWidget()
        self.tab_Control.setObjectName(u"tab_Control")
        self.horizontalLayout_Control = QHBoxLayout(self.tab_Control)
        self.horizontalLayout_Control.setObjectName(u"horizontalLayout_Control")
        self.tableWidget_control = QTableWidget(self.tab_Control)
        self.tableWidget_control.setObjectName(u"tableWidget_control")
        self.tableWidget_control.setEnabled(True)
        self.tableWidget_control.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.tableWidget_control.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        self.horizontalLayout_Control.addWidget(self.tableWidget_control)

        self.tabWidget_main.addTab(self.tab_Control, "")
        self.tab_Order_History = QWidget()
        self.tab_Order_History.setObjectName(u"tab_Order_History")
        self.horizontalLayout_Order_History = QHBoxLayout(self.tab_Order_History)
        self.horizontalLayout_Order_History.setObjectName(u"horizontalLayout_Order_History")
        self.groupBox_Open_Orders = QGroupBox(self.tab_Order_History)
        self.groupBox_Open_Orders.setObjectName(u"groupBox_Open_Orders")
        self.verticalLayout_Open_Orders = QVBoxLayout(self.groupBox_Open_Orders)
        self.verticalLayout_Open_Orders.setObjectName(u"verticalLayout_Open_Orders")
        self.tableWidget_Open_Orders = QTableWidget(self.groupBox_Open_Orders)
        self.tableWidget_Open_Orders.setObjectName(u"tableWidget_Open_Orders")
        self.tableWidget_Open_Orders.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.tableWidget_Open_Orders.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        self.verticalLayout_Open_Orders.addWidget(self.tableWidget_Open_Orders)


        self.horizontalLayout_Order_History.addWidget(self.groupBox_Open_Orders)

        self.groupBox_Closing_Orders = QGroupBox(self.tab_Order_History)
        self.groupBox_Closing_Orders.setObjectName(u"groupBox_Closing_Orders")
        self.horizontalLayout_Closing_Orders = QHBoxLayout(self.groupBox_Closing_Orders)
        self.horizontalLayout_Closing_Orders.setObjectName(u"horizontalLayout_Closing_Orders")
        self.tableWidget_Closing_Orders = QTableWidget(self.groupBox_Closing_Orders)
        self.tableWidget_Closing_Orders.setObjectName(u"tableWidget_Closing_Orders")
        self.tableWidget_Closing_Orders.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.tableWidget_Closing_Orders.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        self.horizontalLayout_Closing_Orders.addWidget(self.tableWidget_Closing_Orders)


        self.horizontalLayout_Order_History.addWidget(self.groupBox_Closing_Orders)

        self.tabWidget_main.addTab(self.tab_Order_History, "")
        self.tab_Visualization = QWidget()
        self.tab_Visualization.setObjectName(u"tab_Visualization")
        self.horizontalLayout_Visualization = QHBoxLayout(self.tab_Visualization)
        self.horizontalLayout_Visualization.setObjectName(u"horizontalLayout_Visualization")
        self.groupBox_Chart = QGroupBox(self.tab_Visualization)
        self.groupBox_Chart.setObjectName(u"groupBox_Chart")
        self.horizontalLayout_Chart = QHBoxLayout(self.groupBox_Chart)
        self.horizontalLayout_Chart.setObjectName(u"horizontalLayout_Chart")
        self.graphicsView = QGraphicsView(self.groupBox_Chart)
        self.graphicsView.setObjectName(u"graphicsView")

        self.horizontalLayout_Chart.addWidget(self.graphicsView)


        self.horizontalLayout_Visualization.addWidget(self.groupBox_Chart)

        self.groupBox_Historical_orders = QGroupBox(self.tab_Visualization)
        self.groupBox_Historical_orders.setObjectName(u"groupBox_Historical_orders")
        self.verticalLayout_Historical_orders = QVBoxLayout(self.groupBox_Historical_orders)
        self.verticalLayout_Historical_orders.setObjectName(u"verticalLayout_Historical_orders")
        self.tableWidget_Historical_orders = QTableWidget(self.groupBox_Historical_orders)
        self.tableWidget_Historical_orders.setObjectName(u"tableWidget_Historical_orders")
        self.tableWidget_Historical_orders.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.tableWidget_Historical_orders.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        self.verticalLayout_Historical_orders.addWidget(self.tableWidget_Historical_orders)


        self.horizontalLayout_Visualization.addWidget(self.groupBox_Historical_orders)

        self.tabWidget_main.addTab(self.tab_Visualization, "")

        self.verticalLayout_centralwidget.addWidget(self.tabWidget_main)

        self.tabWidget_Configuration = QTabWidget(self.centralwidget)
        self.tabWidget_Configuration.setObjectName(u"tabWidget_Configuration")
        self.tabWidget_Configuration.setMaximumSize(QSize(16777215, 200))
        self.tab_Config = QWidget()
        self.tab_Config.setObjectName(u"tab_Config")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tab_Config.sizePolicy().hasHeightForWidth())
        self.tab_Config.setSizePolicy(sizePolicy)
        self.horizontalLayout_Configuration = QHBoxLayout(self.tab_Config)
        self.horizontalLayout_Configuration.setObjectName(u"horizontalLayout_Configuration")
        self.groupBox_Global_Control = QGroupBox(self.tab_Config)
        self.groupBox_Global_Control.setObjectName(u"groupBox_Global_Control")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.groupBox_Global_Control.sizePolicy().hasHeightForWidth())
        self.groupBox_Global_Control.setSizePolicy(sizePolicy1)
        self.groupBox_Global_Control.setMaximumSize(QSize(200, 16777215))
        self.gridLayout_Global_Control = QGridLayout(self.groupBox_Global_Control)
        self.gridLayout_Global_Control.setObjectName(u"gridLayout_Global_Control")
        self.pushButton_All_Start = QPushButton(self.groupBox_Global_Control)
        self.pushButton_All_Start.setObjectName(u"pushButton_All_Start")

        self.gridLayout_Global_Control.addWidget(self.pushButton_All_Start, 0, 0, 1, 1)

        self.pushButton_All_Stop = QPushButton(self.groupBox_Global_Control)
        self.pushButton_All_Stop.setObjectName(u"pushButton_All_Stop")

        self.gridLayout_Global_Control.addWidget(self.pushButton_All_Stop, 0, 1, 1, 1)

        self.pushButton_All_Pause_Order = QPushButton(self.groupBox_Global_Control)
        self.pushButton_All_Pause_Order.setObjectName(u"pushButton_All_Pause_Order")

        self.gridLayout_Global_Control.addWidget(self.pushButton_All_Pause_Order, 1, 0, 1, 1)

        self.pushButton_All_Recovery_Order = QPushButton(self.groupBox_Global_Control)
        self.pushButton_All_Recovery_Order.setObjectName(u"pushButton_All_Recovery_Order")

        self.gridLayout_Global_Control.addWidget(self.pushButton_All_Recovery_Order, 1, 1, 1, 1)

        self.pushButton_All_Sell = QPushButton(self.groupBox_Global_Control)
        self.pushButton_All_Sell.setObjectName(u"pushButton_All_Sell")

        self.gridLayout_Global_Control.addWidget(self.pushButton_All_Sell, 4, 0, 1, 1)

        self.pushButton_All_Clear = QPushButton(self.groupBox_Global_Control)
        self.pushButton_All_Clear.setObjectName(u"pushButton_All_Clear")

        self.gridLayout_Global_Control.addWidget(self.pushButton_All_Clear, 4, 1, 1, 1)


        self.horizontalLayout_Configuration.addWidget(self.groupBox_Global_Control)

        self.groupBox_exchanges = QGroupBox(self.tab_Config)
        self.groupBox_exchanges.setObjectName(u"groupBox_exchanges")
        self.groupBox_exchanges.setMaximumSize(QSize(100, 16777215))
        self.verticalLayout_exchanges = QVBoxLayout(self.groupBox_exchanges)
        self.verticalLayout_exchanges.setObjectName(u"verticalLayout_exchanges")
        self.radioButton_Binance = QRadioButton(self.groupBox_exchanges)
        self.radioButton_Binance.setObjectName(u"radioButton_Binance")
        font = QFont()
        font.setPointSize(12)
        self.radioButton_Binance.setFont(font)

        self.verticalLayout_exchanges.addWidget(self.radioButton_Binance)

        self.radioButton_OKX = QRadioButton(self.groupBox_exchanges)
        self.radioButton_OKX.setObjectName(u"radioButton_OKX")
        self.radioButton_OKX.setFont(font)
        self.radioButton_OKX.setChecked(True)

        self.verticalLayout_exchanges.addWidget(self.radioButton_OKX)


        self.horizontalLayout_Configuration.addWidget(self.groupBox_exchanges)

        self.groupBox_order_mode = QGroupBox(self.tab_Config)
        self.groupBox_order_mode.setObjectName(u"groupBox_order_mode")
        self.groupBox_order_mode.setMaximumSize(QSize(100, 16777215))
        self.verticalLayout_order_mode = QVBoxLayout(self.groupBox_order_mode)
        self.verticalLayout_order_mode.setObjectName(u"verticalLayout_order_mode")
        self.radioButton_Firm_Offer = QRadioButton(self.groupBox_order_mode)
        self.radioButton_Firm_Offer.setObjectName(u"radioButton_Firm_Offer")

        self.verticalLayout_order_mode.addWidget(self.radioButton_Firm_Offer)

        self.radioButton_Online_Simulation = QRadioButton(self.groupBox_order_mode)
        self.radioButton_Online_Simulation.setObjectName(u"radioButton_Online_Simulation")

        self.verticalLayout_order_mode.addWidget(self.radioButton_Online_Simulation)

        self.radioButton_Local_simulation = QRadioButton(self.groupBox_order_mode)
        self.radioButton_Local_simulation.setObjectName(u"radioButton_Local_simulation")
        self.radioButton_Local_simulation.setChecked(True)

        self.verticalLayout_order_mode.addWidget(self.radioButton_Local_simulation)


        self.horizontalLayout_Configuration.addWidget(self.groupBox_order_mode)

        self.groupBox_OpenAndClose_log = QGroupBox(self.tab_Config)
        self.groupBox_OpenAndClose_log.setObjectName(u"groupBox_OpenAndClose_log")
        self.horizontalLayout = QHBoxLayout(self.groupBox_OpenAndClose_log)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.tableWidget_OpenAndClose_log = QTableWidget(self.groupBox_OpenAndClose_log)
        self.tableWidget_OpenAndClose_log.setObjectName(u"tableWidget_OpenAndClose_log")
        self.tableWidget_OpenAndClose_log.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.tableWidget_OpenAndClose_log.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        self.horizontalLayout.addWidget(self.tableWidget_OpenAndClose_log)


        self.horizontalLayout_Configuration.addWidget(self.groupBox_OpenAndClose_log)

        self.tabWidget_Configuration.addTab(self.tab_Config, "")
        self.tab_API = QWidget()
        self.tab_API.setObjectName(u"tab_API")
        self.horizontalLayout_API = QHBoxLayout(self.tab_API)
        self.horizontalLayout_API.setObjectName(u"horizontalLayout_API")
        self.groupBox_Binance_API = QGroupBox(self.tab_API)
        self.groupBox_Binance_API.setObjectName(u"groupBox_Binance_API")
        self.gridLayout_Binance = QGridLayout(self.groupBox_Binance_API)
        self.gridLayout_Binance.setObjectName(u"gridLayout_Binance")
        self.lineEdit_Binance_SKEY = QLineEdit(self.groupBox_Binance_API)
        self.lineEdit_Binance_SKEY.setObjectName(u"lineEdit_Binance_SKEY")
        self.lineEdit_Binance_SKEY.setEchoMode(QLineEdit.EchoMode.Normal)

        self.gridLayout_Binance.addWidget(self.lineEdit_Binance_SKEY, 0, 1, 1, 1)

        self.label_Binance_SKEY = QLabel(self.groupBox_Binance_API)
        self.label_Binance_SKEY.setObjectName(u"label_Binance_SKEY")

        self.gridLayout_Binance.addWidget(self.label_Binance_SKEY, 0, 0, 1, 1)

        self.lineEdit_Binance_AKEY = QLineEdit(self.groupBox_Binance_API)
        self.lineEdit_Binance_AKEY.setObjectName(u"lineEdit_Binance_AKEY")
        self.lineEdit_Binance_AKEY.setEchoMode(QLineEdit.EchoMode.Password)

        self.gridLayout_Binance.addWidget(self.lineEdit_Binance_AKEY, 1, 1, 1, 1)

        self.label_Binance_AKEY = QLabel(self.groupBox_Binance_API)
        self.label_Binance_AKEY.setObjectName(u"label_Binance_AKEY")

        self.gridLayout_Binance.addWidget(self.label_Binance_AKEY, 1, 0, 1, 1)

        self.pushButton_Binance_save = QPushButton(self.groupBox_Binance_API)
        self.pushButton_Binance_save.setObjectName(u"pushButton_Binance_save")

        self.gridLayout_Binance.addWidget(self.pushButton_Binance_save, 2, 1, 1, 1)


        self.horizontalLayout_API.addWidget(self.groupBox_Binance_API)

        self.groupBox_OKX_API = QGroupBox(self.tab_API)
        self.groupBox_OKX_API.setObjectName(u"groupBox_OKX_API")
        self.gridLayout_OKX = QGridLayout(self.groupBox_OKX_API)
        self.gridLayout_OKX.setObjectName(u"gridLayout_OKX")
        self.label_OKX_PASS = QLabel(self.groupBox_OKX_API)
        self.label_OKX_PASS.setObjectName(u"label_OKX_PASS")
        self.label_OKX_PASS.setAlignment(Qt.AlignmentFlag.AlignLeading|Qt.AlignmentFlag.AlignLeft|Qt.AlignmentFlag.AlignVCenter)

        self.gridLayout_OKX.addWidget(self.label_OKX_PASS, 2, 0, 1, 1)

        self.label_OKX_SKEY = QLabel(self.groupBox_OKX_API)
        self.label_OKX_SKEY.setObjectName(u"label_OKX_SKEY")

        self.gridLayout_OKX.addWidget(self.label_OKX_SKEY, 0, 0, 1, 1)

        self.lineEdit_OKX_PASS = QLineEdit(self.groupBox_OKX_API)
        self.lineEdit_OKX_PASS.setObjectName(u"lineEdit_OKX_PASS")
        self.lineEdit_OKX_PASS.setEchoMode(QLineEdit.EchoMode.Password)

        self.gridLayout_OKX.addWidget(self.lineEdit_OKX_PASS, 2, 2, 1, 1)

        self.lineEdit_OKX_SKEY = QLineEdit(self.groupBox_OKX_API)
        self.lineEdit_OKX_SKEY.setObjectName(u"lineEdit_OKX_SKEY")
        self.lineEdit_OKX_SKEY.setEchoMode(QLineEdit.EchoMode.Normal)

        self.gridLayout_OKX.addWidget(self.lineEdit_OKX_SKEY, 0, 2, 1, 1)

        self.lineEdit_OKX_AKEY = QLineEdit(self.groupBox_OKX_API)
        self.lineEdit_OKX_AKEY.setObjectName(u"lineEdit_OKX_AKEY")
        self.lineEdit_OKX_AKEY.setEchoMode(QLineEdit.EchoMode.Password)

        self.gridLayout_OKX.addWidget(self.lineEdit_OKX_AKEY, 1, 2, 1, 1)

        self.label_OKX_AKEY = QLabel(self.groupBox_OKX_API)
        self.label_OKX_AKEY.setObjectName(u"label_OKX_AKEY")

        self.gridLayout_OKX.addWidget(self.label_OKX_AKEY, 1, 0, 1, 1)

        self.pushButton_OKX_save = QPushButton(self.groupBox_OKX_API)
        self.pushButton_OKX_save.setObjectName(u"pushButton_OKX_save")

        self.gridLayout_OKX.addWidget(self.pushButton_OKX_save, 3, 2, 1, 1)


        self.horizontalLayout_API.addWidget(self.groupBox_OKX_API)

        self.tabWidget_Configuration.addTab(self.tab_API, "")

        self.verticalLayout_centralwidget.addWidget(self.tabWidget_Configuration)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        self.statusbar.setSizeGripEnabled(True)
        MainWindow.setStatusBar(self.statusbar)
        self.menubar = QMenuBar(MainWindow)
        self.menubar.setObjectName(u"menubar")
        self.menubar.setGeometry(QRect(0, 0, 1440, 33))
        MainWindow.setMenuBar(self.menubar)

        self.retranslateUi(MainWindow)

        self.tabWidget_main.setCurrentIndex(0)
        self.tabWidget_Configuration.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"\u73b0\u8d27\u91cf\u5316\u8f6f\u4ef6", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_Control), QCoreApplication.translate("MainWindow", u"\u4e2d\u63a7", None))
        self.groupBox_Open_Orders.setTitle(QCoreApplication.translate("MainWindow", u"\u6301\u4ed3", None))
        self.groupBox_Closing_Orders.setTitle(QCoreApplication.translate("MainWindow", u"\u5e73\u4ed3", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_Order_History), QCoreApplication.translate("MainWindow", u"\u8bb0\u5f55", None))
        self.groupBox_Chart.setTitle(QCoreApplication.translate("MainWindow", u"\u56fe\u8868", None))
        self.groupBox_Historical_orders.setTitle(QCoreApplication.translate("MainWindow", u"\u5386\u53f2\u6570\u636e", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_Visualization), QCoreApplication.translate("MainWindow", u"\u53ef\u89c6\u5316", None))
        self.groupBox_Global_Control.setTitle(QCoreApplication.translate("MainWindow", u"\u63a7\u5236", None))
        self.pushButton_All_Start.setText(QCoreApplication.translate("MainWindow", u"\u5168\u90e8\u5f00\u59cb", None))
        self.pushButton_All_Stop.setText(QCoreApplication.translate("MainWindow", u"\u5168\u90e8\u505c\u6b62", None))
        self.pushButton_All_Pause_Order.setText(QCoreApplication.translate("MainWindow", u"\u5168\u90e8\u6682\u505c\u8865\u5355", None))
        self.pushButton_All_Recovery_Order.setText(QCoreApplication.translate("MainWindow", u"\u5168\u90e8\u6062\u590d\u8865\u5355", None))
        self.pushButton_All_Sell.setText(QCoreApplication.translate("MainWindow", u"\u4e00\u952e\u5e73\u4ed3", None))
        self.pushButton_All_Clear.setText(QCoreApplication.translate("MainWindow", u"\u6e05\u7a7a\u5168\u90e8", None))
        self.groupBox_exchanges.setTitle(QCoreApplication.translate("MainWindow", u"\u4ea4\u6613\u6240", None))
        self.radioButton_Binance.setText(QCoreApplication.translate("MainWindow", u"\u5e01\u5b89", None))
        self.radioButton_OKX.setText(QCoreApplication.translate("MainWindow", u"OKX", None))
        self.groupBox_order_mode.setTitle(QCoreApplication.translate("MainWindow", u"\u4e0b\u5355\u6a21\u5f0f", None))
        self.radioButton_Firm_Offer.setText(QCoreApplication.translate("MainWindow", u"\u5b9e\u76d8\u4e0b\u5355", None))
        self.radioButton_Online_Simulation.setText(QCoreApplication.translate("MainWindow", u"\u7ebf\u4e0a\u6a21\u62df", None))
        self.radioButton_Local_simulation.setText(QCoreApplication.translate("MainWindow", u"\u672c\u5730\u6a21\u62df", None))
        self.groupBox_OpenAndClose_log.setTitle(QCoreApplication.translate("MainWindow", u"\u5f00\u5e73\u4ed3\u65e5\u5fd7", None))
        self.tabWidget_Configuration.setTabText(self.tabWidget_Configuration.indexOf(self.tab_Config), QCoreApplication.translate("MainWindow", u"\u914d\u7f6e", None))
        self.groupBox_Binance_API.setTitle(QCoreApplication.translate("MainWindow", u"\u5e01\u5b89", None))
        self.label_Binance_SKEY.setText(QCoreApplication.translate("MainWindow", u"S_Key:", None))
        self.label_Binance_AKEY.setText(QCoreApplication.translate("MainWindow", u"A_Key:", None))
        self.pushButton_Binance_save.setText(QCoreApplication.translate("MainWindow", u"\u4fdd\u5b58", None))
        self.groupBox_OKX_API.setTitle(QCoreApplication.translate("MainWindow", u"OKX", None))
        self.label_OKX_PASS.setText(QCoreApplication.translate("MainWindow", u"Pass:", None))
        self.label_OKX_SKEY.setText(QCoreApplication.translate("MainWindow", u"S_Key:", None))
        self.label_OKX_AKEY.setText(QCoreApplication.translate("MainWindow", u"A_Key:", None))
        self.pushButton_OKX_save.setText(QCoreApplication.translate("MainWindow", u"\u4fdd\u5b58", None))
        self.tabWidget_Configuration.setTabText(self.tabWidget_Configuration.indexOf(self.tab_API), QCoreApplication.translate("MainWindow", u"API\u914d\u7f6e", None))
    # retranslateUi

