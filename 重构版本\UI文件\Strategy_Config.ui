<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>StrategyConfigWindow</class>
 <widget class="QDialog" name="StrategyConfigWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>650</width>
    <height>500</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>策略配置</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_main">
   <property name="spacing">
    <number>8</number>
   </property>
   <property name="leftMargin">
    <number>10</number>
   </property>
   <property name="topMargin">
    <number>10</number>
   </property>
   <property name="rightMargin">
    <number>10</number>
   </property>
   <property name="bottomMargin">
    <number>10</number>
   </property>
   <item>
    <widget class="QGroupBox" name="groupBox_trading_pair">
     <property name="title">
      <string>选择交易对</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_trading_pair">
      <property name="spacing">
       <number>6</number>
      </property>
      <property name="leftMargin">
       <number>6</number>
      </property>
      <property name="topMargin">
       <number>6</number>
      </property>
      <property name="rightMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>6</number>
      </property>
      <item>
       <widget class="QComboBox" name="comboBox_trading_pair">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>0</height>
         </size>
        </property>
        <property name="editable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_add_all">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>30</height>
         </size>
        </property>
        <property name="text">
         <string>添加全部</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_trading_pair">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QTabWidget" name="tabWidget_strategy">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab_custom_strategy">
      <attribute name="title">
       <string>自定义策略</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_custom">
       <property name="spacing">
        <number>6</number>
       </property>
       <item>
        <widget class="QScrollArea" name="scrollArea_custom">
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <widget class="QWidget" name="scrollAreaWidgetContents_custom">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>-110</y>
            <width>592</width>
            <height>435</height>
           </rect>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_custom_content">
           <property name="spacing">
            <number>6</number>
           </property>
           <property name="leftMargin">
            <number>6</number>
           </property>
           <property name="topMargin">
            <number>6</number>
           </property>
           <property name="rightMargin">
            <number>6</number>
           </property>
           <property name="bottomMargin">
            <number>6</number>
           </property>
           <item>
            <widget class="QGroupBox" name="groupBox_first_order">
             <property name="title">
              <string>首单金额</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_first_order">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_first_amount">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> U</string>
                </property>
                <property name="maximum">
                 <double>99999.000000000000000</double>
                </property>
                <property name="value">
                 <double>10.000000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_first_order">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_drop_percent">
             <property name="title">
              <string>跌幅多少加仓</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_drop_percent">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_drop_percent">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> %</string>
                </property>
                <property name="maximum">
                 <double>100.000000000000000</double>
                </property>
                <property name="value">
                 <double>1.000000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_drop_percent">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_position_multiplier">
             <property name="title">
              <string>加仓倍数</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_position_multiplier">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_position_multiplier">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> 倍</string>
                </property>
                <property name="minimum">
                 <double>1.000000000000000</double>
                </property>
                <property name="maximum">
                 <double>10.000000000000000</double>
                </property>
                <property name="value">
                 <double>1.300000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_position_multiplier">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_profit_percent">
             <property name="title">
              <string>百分比止盈比例</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_profit_percent">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_profit_percent">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> %</string>
                </property>
                <property name="maximum">
                 <double>100.000000000000000</double>
                </property>
                <property name="value">
                 <double>1.500000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_profit_percent">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_max_orders">
             <property name="title">
              <string>最大持仓单数</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_max_orders">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QSpinBox" name="spinBox_max_orders">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> 次</string>
                </property>
                <property name="minimum">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <number>100</number>
                </property>
                <property name="value">
                 <number>10</number>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_max_orders">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_profit_stop">
             <property name="title">
              <string>盈利多少停止机器人</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_profit_stop">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_profit_stop">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> U</string>
                </property>
                <property name="maximum">
                 <double>99999.000000000000000</double>
                </property>
                <property name="value">
                 <double>100.000000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_profit_stop">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_dynamic_position">
             <property name="title">
              <string>动态持仓</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_dynamic_position">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QCheckBox" name="checkBox_dynamic_position">
                <property name="text">
                 <string>开启动态持仓</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_dynamic_position">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_rebound_percent">
             <property name="title">
              <string>百分比反弹多少补仓</string>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_rebound_percent">
              <property name="spacing">
               <number>6</number>
              </property>
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>4</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>4</number>
              </property>
              <item>
               <widget class="QDoubleSpinBox" name="doubleSpinBox_rebound_percent">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="suffix">
                 <string> %</string>
                </property>
                <property name="maximum">
                 <double>100.000000000000000</double>
                </property>
                <property name="value">
                 <double>0.300000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_rebound_percent">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_ai_strategy">
      <attribute name="title">
       <string>AI策略</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_ai">
       <property name="spacing">
        <number>6</number>
       </property>
       <item>
        <widget class="QGroupBox" name="groupBox_strategy_selection">
         <property name="title">
          <string>策略类型选择</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_strategy_selection">
          <property name="spacing">
           <number>20</number>
          </property>
          <property name="leftMargin">
           <number>6</number>
          </property>
          <property name="topMargin">
           <number>4</number>
          </property>
          <property name="rightMargin">
           <number>6</number>
          </property>
          <property name="bottomMargin">
           <number>4</number>
          </property>
          <item>
           <widget class="QRadioButton" name="radioButton_aggressive">
            <property name="text">
             <string>进攻型策略</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="radioButton_defensive">
            <property name="text">
             <string>防守型策略</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="radioButton_universal">
            <property name="text">
             <string>通用型策略</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_strategy">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_sub_strategy">
         <property name="title">
          <string>子策略选择</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_sub_strategy">
          <property name="spacing">
           <number>4</number>
          </property>
          <property name="leftMargin">
           <number>6</number>
          </property>
          <property name="topMargin">
           <number>4</number>
          </property>
          <property name="rightMargin">
           <number>6</number>
          </property>
          <property name="bottomMargin">
           <number>4</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_sub_strategies">
            <property name="spacing">
             <number>15</number>
            </property>
            <item>
             <widget class="QRadioButton" name="radioButton_universal_a">
              <property name="visible">
               <bool>false</bool>
              </property>
              <property name="text">
               <string>A</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_crazy">
              <property name="text">
               <string>疯狂型</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_aggressive_sub">
              <property name="text">
               <string>激进型</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_balanced">
              <property name="text">
               <string>平衡型</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_stable">
              <property name="text">
               <string>稳健型</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_conservative">
              <property name="text">
               <string>保守型</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_sub_strategies">
              <property name="orientation">
               <enum>Qt::Orientation::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_capital_setting">
         <property name="title">
          <string>投入本金设置</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_capital">
          <property name="spacing">
           <number>6</number>
          </property>
          <property name="leftMargin">
           <number>6</number>
          </property>
          <property name="topMargin">
           <number>4</number>
          </property>
          <property name="rightMargin">
           <number>6</number>
          </property>
          <property name="bottomMargin">
           <number>4</number>
          </property>
          <item>
           <widget class="QLabel" name="label_capital">
            <property name="text">
             <string>投入本金:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDoubleSpinBox" name="doubleSpinBox_capital">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="suffix">
             <string> U</string>
            </property>
            <property name="maximum">
             <double>99999.000000000000000</double>
            </property>
            <property name="value">
             <double>100.000000000000000</double>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_capital">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_ai">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_buttons">
     <property name="spacing">
      <number>6</number>
     </property>
     <item>
      <spacer name="horizontalSpacer_buttons">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_add_strategy">
       <property name="minimumSize">
        <size>
         <width>100</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>添加策略</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_cancel">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>30</height>
        </size>
       </property>
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>pushButton_cancel</sender>
   <signal>clicked()</signal>
   <receiver>StrategyConfigWindow</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>580</x>
     <y>470</y>
    </hint>
    <hint type="destinationlabel">
     <x>325</x>
     <y>250</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
