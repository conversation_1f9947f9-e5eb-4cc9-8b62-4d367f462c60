# -*- coding: utf-8 -*-
"""
服务注册器类
负责统一管理所有服务的注册流程
"""

import logging
from typing import Optional
from .类_服务容器 import 类_服务容器

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)


class 类_服务注册器:
    """
    服务注册器类
    提供统一的服务注册接口，管理服务注册的顺序和依赖关系
    """
    
    def __init__(self, 参_服务容器: Optional[类_服务容器] = None):
        """
        初始化服务注册器
        
        @param 参_服务容器 (Optional[类_服务容器]): 服务容器实例，如果为None则使用全局容器
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 设置服务容器
        # 如果没有传入容器，则使用全局容器实例
        if 参_服务容器 is None:
            from .类_服务容器 import 全_服务容器
            self.服务容器 = 全_服务容器
        else:
            self.服务容器 = 参_服务容器
        
        # 步骤2: 设置模块名称
        self.类_模块名 = "服务注册器"
        
        logger.info("服务注册器初始化完成")
        print("服务注册器初始化完成")
    
    def 注册所有服务(self) -> None:
        """
        按照正确的依赖顺序注册所有服务
        
        @return None
        @exception Exception: 注册过程中可能发生的未知错误
        """
        try:
            # 步骤1: 记录开始注册
            logger.info("开始注册所有服务")
            print("开始注册所有服务")
            
            # 步骤2: 按依赖顺序注册服务
            # 基础服务 -> 工具服务 -> 数据访问服务 -> 业务服务 -> UI控制器
            self.注册基础服务()
            self.注册工具服务()
            self.注册数据访问服务()
            self.注册业务服务()
            self.注册UI控制器服务()
            
            # 步骤3: 记录注册完成
            已注册服务列表 = self.服务容器.获取已注册服务列表()
            logger.info(f"所有服务注册完成，共注册 {len(已注册服务列表)} 个服务")
            print(f"所有服务注册完成，共注册 {len(已注册服务列表)} 个服务")
            
            # 步骤4: 显示已注册的服务列表
            for 服务名称 in 已注册服务列表:
                logger.debug(f"已注册服务: {服务名称}")
                print(f"已注册服务: {服务名称}")
            
        except Exception as e:
            logger.error(f"注册所有服务失败: {e}", exc_info=True)
            print(f"注册所有服务失败: {e}")
            raise
    
    def 注册基础服务(self) -> None:
        """
        注册基础服务
        这些服务是其他服务的基础依赖
        
        @return None
        @exception Exception: 注册过程中可能发生的未知错误
        """
        try:
            logger.info("开始注册基础服务")
            
            # 步骤1: 注册日志服务
            # 日志服务不依赖其他服务，最先注册
            self.服务容器.注册服务(
                参_服务名称="日志服务",
                参_服务工厂=self._创建日志服务,
                参_是否单例=True,
                参_依赖列表=[]
            )
            
            # 步骤2: 注册配置服务
            # 配置服务不依赖其他服务
            self.服务容器.注册服务(
                参_服务名称="配置服务",
                参_服务工厂=self._创建配置服务,
                参_是否单例=True,
                参_依赖列表=[]
            )
            
            # 步骤3: 注册数据库服务
            # 数据库服务依赖配置服务
            self.服务容器.注册服务(
                参_服务名称="数据库服务",
                参_服务工厂=self._创建数据库服务,
                参_是否单例=True,
                参_依赖列表=["配置服务"]
            )
            
            # 步骤4: 注册UI更新服务
            # UI更新服务将在UI控制器创建后初始化
            self.服务容器.注册服务(
                参_服务名称="UI更新服务",
                参_服务工厂=self._创建UI更新服务,
                参_是否单例=True,
                参_依赖列表=[]
            )
            
            logger.info("基础服务注册完成")
            
        except Exception as e:
            logger.error(f"注册基础服务失败: {e}", exc_info=True)
            raise
    
    def 注册工具服务(self) -> None:
        """
        注册工具服务
        这些服务提供通用的工具功能
        
        @return None
        @exception Exception: 注册过程中可能发生的未知错误
        """
        try:
            logger.info("开始注册工具服务")
            
            # 步骤1: 注册加密服务
            # 加密服务不依赖其他服务
            self.服务容器.注册服务(
                参_服务名称="加密服务",
                参_服务工厂=self._创建加密服务,
                参_是否单例=True,
                参_依赖列表=[]
            )
            
            # 步骤2: 注册API服务
            # API服务不依赖其他服务
            self.服务容器.注册服务(
                参_服务名称="API服务",
                参_服务工厂=self._创建API服务,
                参_是否单例=True,
                参_依赖列表=[]
            )
            
            # 步骤3: 注册计算服务
            # 计算服务依赖数据库服务和API服务
            self.服务容器.注册服务(
                参_服务名称="计算服务",
                参_服务工厂=self._创建计算服务,
                参_是否单例=True,
                参_依赖列表=["数据库服务", "API服务"]
            )
            
            logger.info("工具服务注册完成")
            
        except Exception as e:
            logger.error(f"注册工具服务失败: {e}", exc_info=True)
            raise
    
    def 注册数据访问服务(self) -> None:
        """
        注册数据访问服务
        这些服务负责数据库操作
        
        @return None
        @exception Exception: 注册过程中可能发生的未知错误
        """
        try:
            logger.info("开始注册数据访问服务")
            
            # 所有数据访问服务都依赖数据库服务
            数据访问服务列表 = [
                "机器人数据服务",
                "配置数据服务", 
                "交易对数据服务",
                "持仓数据服务",
                "交易日志数据服务"
            ]
            
            for 服务名称 in 数据访问服务列表:
                self.服务容器.注册服务(
                    参_服务名称=服务名称,
                    参_服务工厂=lambda 名称=服务名称: self._创建数据访问服务(名称),
                    参_是否单例=True,
                    参_依赖列表=["数据库服务"]
                )
            
            logger.info("数据访问服务注册完成")
            
        except Exception as e:
            logger.error(f"注册数据访问服务失败: {e}", exc_info=True)
            raise
    
    def 注册业务服务(self) -> None:
        """
        注册业务服务
        这些服务实现具体的业务逻辑
        
        @return None
        @exception Exception: 注册过程中可能发生的未知错误
        """
        try:
            logger.info("开始注册业务服务")
            
            # 步骤1: 注册指标服务
            # 指标服务不依赖其他业务服务
            self.服务容器.注册服务(
                参_服务名称="指标服务",
                参_服务工厂=self._创建指标服务,
                参_是否单例=True,
                参_依赖列表=[]
            )
            
            # 步骤2: 注册策略服务
            # 策略服务依赖指标服务
            self.服务容器.注册服务(
                参_服务名称="策略服务",
                参_服务工厂=self._创建策略服务,
                参_是否单例=True,
                参_依赖列表=["指标服务"]
            )
            
            # 步骤3: 注册机器人服务
            # 机器人服务依赖机器人数据服务
            self.服务容器.注册服务(
                参_服务名称="机器人服务",
                参_服务工厂=self._创建机器人服务,
                参_是否单例=True,
                参_依赖列表=["机器人数据服务"]
            )
            
            # 步骤4: 注册交易服务
            # 交易服务依赖多个服务
            self.服务容器.注册服务(
                参_服务名称="交易服务",
                参_服务工厂=self._创建交易服务,
                参_是否单例=True,
                参_依赖列表=["API服务", "策略服务", "计算服务"]
            )
            
            # 步骤5: 注册线程管理服务
            # 线程管理服务依赖UI更新服务和交易服务
            self.服务容器.注册服务(
                参_服务名称="线程管理服务",
                参_服务工厂=self._创建线程管理服务,
                参_是否单例=True,
                参_依赖列表=["UI更新服务", "交易服务"]
            )
            
            logger.info("业务服务注册完成")
            
        except Exception as e:
            logger.error(f"注册业务服务失败: {e}", exc_info=True)
            raise
    
    def 注册UI控制器服务(self) -> None:
        """
        注册UI控制器服务
        这些服务负责UI逻辑控制
        
        @return None
        @exception Exception: 注册过程中可能发生的未知错误
        """
        try:
            logger.info("开始注册UI控制器服务")
            
            # 步骤1: 注册主窗口控制器
            # 主窗口控制器依赖多个服务
            self.服务容器.注册服务(
                参_服务名称="主窗口控制器",
                参_服务工厂=self._创建主窗口控制器,
                参_是否单例=True,
                参_依赖列表=["数据库服务", "线程管理服务", "UI更新服务"]
            )
            
            # 步骤2: 注册策略窗口控制器
            # 策略窗口控制器依赖数据库服务和策略服务
            self.服务容器.注册服务(
                参_服务名称="策略窗口控制器",
                参_服务工厂=self._创建策略窗口控制器,
                参_是否单例=False,  # 策略窗口可能有多个实例
                参_依赖列表=["数据库服务", "策略服务"]
            )
            
            logger.info("UI控制器服务注册完成")
            
        except Exception as e:
            logger.error(f"注册UI控制器服务失败: {e}", exc_info=True)
            raise
    
    # ========== 服务工厂方法 ==========
    # 基础服务工厂方法

    def _创建日志服务(self):
        """创建日志服务的工厂方法"""
        from 重构版本.基础服务.类_日志服务 import 类_日志服务
        return 类_日志服务()

    def _创建配置服务(self):
        """创建配置服务的工厂方法"""
        from 重构版本.基础服务.类_配置服务 import 类_配置服务
        return 类_配置服务()

    def _创建数据库服务(self, 参_配置服务):
        """创建数据库服务的工厂方法"""
        from 重构版本.基础服务.类_数据库服务 import 类_数据库服务
        return 类_数据库服务(参_配置服务)

    def _创建UI更新服务(self):
        """创建UI更新服务的工厂方法"""
        from 重构版本.基础服务.类_UI更新服务 import 类_UI更新服务
        return 类_UI更新服务()
    
    def _创建加密服务(self):
        """创建加密服务的工厂方法"""
        from 重构版本.工具服务.类_加密服务 import 类_加密服务
        return 类_加密服务()

    def _创建API服务(self):
        """创建API服务的工厂方法"""
        from 重构版本.工具服务.类_API服务 import 类_API服务
        return 类_API服务()

    def _创建计算服务(self, 参_数据库服务, 参_API服务):
        """创建计算服务的工厂方法"""
        from 重构版本.工具服务.类_计算服务 import 类_计算服务
        return 类_计算服务(参_数据库服务, 参_API服务)
    
    def _创建数据访问服务(self, 参_服务名称):
        """创建数据访问服务的工厂方法"""
        # 将在数据访问层实现时补充
        pass
    
    def _创建指标服务(self):
        """创建指标服务的工厂方法"""
        # 将在业务服务层实现时补充
        pass
    
    def _创建策略服务(self, 参_指标服务):
        """创建策略服务的工厂方法"""
        # 将在业务服务层实现时补充
        pass
    
    def _创建机器人服务(self, 参_机器人数据服务):
        """创建机器人服务的工厂方法"""
        # 将在业务服务层实现时补充
        pass
    
    def _创建交易服务(self, 参_API服务, 参_策略服务, 参_计算服务):
        """创建交易服务的工厂方法"""
        # 将在业务服务层实现时补充
        pass
    
    def _创建线程管理服务(self, 参_UI更新服务, 参_交易服务):
        """创建线程管理服务的工厂方法"""
        # 将在业务服务层实现时补充
        pass
    
    def _创建主窗口控制器(self, 参_数据库服务, 参_线程管理服务, 参_UI更新服务):
        """创建主窗口控制器的工厂方法"""
        # 将在UI控制器层实现时补充
        pass
    
    def _创建策略窗口控制器(self, 参_数据库服务, 参_策略服务):
        """创建策略窗口控制器的工厂方法"""
        # 将在UI控制器层实现时补充
        pass
