# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'Strategy_Edit.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>urs<PERSON>,
    <PERSON><PERSON><PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractButton, QApplication, QCheckBox, QDialog,
    QDialogButtonBox, QDoubleSpinBox, QGridLayout, QGroupBox,
    QHBoxLayout, QLabel, QRadioButton, QScrollArea,
    QSizePolicy, QSpacerItem, QSpinBox, QStackedWidget,
    QVBoxLayout, QWidget)

class Ui_StrategyEditWindow(object):
    def setupUi(self, StrategyEditWindow):
        if not StrategyEditWindow.objectName():
            StrategyEditWindow.setObjectName(u"StrategyEditWindow")
        StrategyEditWindow.resize(700, 600)
        self.verticalLayout_main = QVBoxLayout(StrategyEditWindow)
        self.verticalLayout_main.setSpacing(10)
        self.verticalLayout_main.setObjectName(u"verticalLayout_main")
        self.verticalLayout_main.setContentsMargins(15, 15, 15, 15)
        self.groupBox_current_strategy_info = QGroupBox(StrategyEditWindow)
        self.groupBox_current_strategy_info.setObjectName(u"groupBox_current_strategy_info")
        self.gridLayout_strategy_info = QGridLayout(self.groupBox_current_strategy_info)
        self.gridLayout_strategy_info.setSpacing(8)
        self.gridLayout_strategy_info.setObjectName(u"gridLayout_strategy_info")
        self.gridLayout_strategy_info.setContentsMargins(10, 10, 10, 10)
        self.label_trading_pair = QLabel(self.groupBox_current_strategy_info)
        self.label_trading_pair.setObjectName(u"label_trading_pair")
        font = QFont()
        font.setBold(True)
        self.label_trading_pair.setFont(font)

        self.gridLayout_strategy_info.addWidget(self.label_trading_pair, 0, 0, 1, 1)

        self.label_trading_pair_value = QLabel(self.groupBox_current_strategy_info)
        self.label_trading_pair_value.setObjectName(u"label_trading_pair_value")

        self.gridLayout_strategy_info.addWidget(self.label_trading_pair_value, 0, 1, 1, 1)

        self.horizontalSpacer_info_1 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_strategy_info.addItem(self.horizontalSpacer_info_1, 0, 2, 1, 1)

        self.label_strategy_type = QLabel(self.groupBox_current_strategy_info)
        self.label_strategy_type.setObjectName(u"label_strategy_type")
        self.label_strategy_type.setFont(font)

        self.gridLayout_strategy_info.addWidget(self.label_strategy_type, 1, 0, 1, 1)

        self.label_strategy_type_value = QLabel(self.groupBox_current_strategy_info)
        self.label_strategy_type_value.setObjectName(u"label_strategy_type_value")

        self.gridLayout_strategy_info.addWidget(self.label_strategy_type_value, 1, 1, 1, 1)

        self.label_create_time = QLabel(self.groupBox_current_strategy_info)
        self.label_create_time.setObjectName(u"label_create_time")
        self.label_create_time.setFont(font)

        self.gridLayout_strategy_info.addWidget(self.label_create_time, 2, 0, 1, 1)

        self.label_create_time_value = QLabel(self.groupBox_current_strategy_info)
        self.label_create_time_value.setObjectName(u"label_create_time_value")

        self.gridLayout_strategy_info.addWidget(self.label_create_time_value, 2, 1, 1, 1)

        self.label_current_status = QLabel(self.groupBox_current_strategy_info)
        self.label_current_status.setObjectName(u"label_current_status")
        self.label_current_status.setFont(font)

        self.gridLayout_strategy_info.addWidget(self.label_current_status, 3, 0, 1, 1)

        self.label_current_status_value = QLabel(self.groupBox_current_strategy_info)
        self.label_current_status_value.setObjectName(u"label_current_status_value")

        self.gridLayout_strategy_info.addWidget(self.label_current_status_value, 3, 1, 1, 1)


        self.verticalLayout_main.addWidget(self.groupBox_current_strategy_info)

        self.groupBox_edit_options = QGroupBox(StrategyEditWindow)
        self.groupBox_edit_options.setObjectName(u"groupBox_edit_options")
        self.horizontalLayout_edit_options = QHBoxLayout(self.groupBox_edit_options)
        self.horizontalLayout_edit_options.setSpacing(20)
        self.horizontalLayout_edit_options.setObjectName(u"horizontalLayout_edit_options")
        self.horizontalLayout_edit_options.setContentsMargins(10, 10, 10, 10)
        self.radioButton_edit_parameters = QRadioButton(self.groupBox_edit_options)
        self.radioButton_edit_parameters.setObjectName(u"radioButton_edit_parameters")
        self.radioButton_edit_parameters.setChecked(True)

        self.horizontalLayout_edit_options.addWidget(self.radioButton_edit_parameters)

        self.radioButton_change_strategy = QRadioButton(self.groupBox_edit_options)
        self.radioButton_change_strategy.setObjectName(u"radioButton_change_strategy")
        self.radioButton_change_strategy.setEnabled(False)

        self.horizontalLayout_edit_options.addWidget(self.radioButton_change_strategy)

        self.horizontalSpacer_edit_options = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_edit_options.addItem(self.horizontalSpacer_edit_options)


        self.verticalLayout_main.addWidget(self.groupBox_edit_options)

        self.stackedWidget_edit_content = QStackedWidget(StrategyEditWindow)
        self.stackedWidget_edit_content.setObjectName(u"stackedWidget_edit_content")
        self.page_edit_parameters = QWidget()
        self.page_edit_parameters.setObjectName(u"page_edit_parameters")
        self.verticalLayout_edit_parameters = QVBoxLayout(self.page_edit_parameters)
        self.verticalLayout_edit_parameters.setSpacing(8)
        self.verticalLayout_edit_parameters.setObjectName(u"verticalLayout_edit_parameters")
        self.scrollArea_parameters = QScrollArea(self.page_edit_parameters)
        self.scrollArea_parameters.setObjectName(u"scrollArea_parameters")
        self.scrollArea_parameters.setWidgetResizable(True)
        self.scrollAreaWidgetContents_parameters = QWidget()
        self.scrollAreaWidgetContents_parameters.setObjectName(u"scrollAreaWidgetContents_parameters")
        self.scrollAreaWidgetContents_parameters.setGeometry(QRect(0, -143, 638, 457))
        self.verticalLayout_parameters_content = QVBoxLayout(self.scrollAreaWidgetContents_parameters)
        self.verticalLayout_parameters_content.setSpacing(8)
        self.verticalLayout_parameters_content.setObjectName(u"verticalLayout_parameters_content")
        self.verticalLayout_parameters_content.setContentsMargins(10, 10, 10, 10)
        self.groupBox_edit_first_amount = QGroupBox(self.scrollAreaWidgetContents_parameters)
        self.groupBox_edit_first_amount.setObjectName(u"groupBox_edit_first_amount")
        self.horizontalLayout_edit_first_amount = QHBoxLayout(self.groupBox_edit_first_amount)
        self.horizontalLayout_edit_first_amount.setSpacing(6)
        self.horizontalLayout_edit_first_amount.setObjectName(u"horizontalLayout_edit_first_amount")
        self.horizontalLayout_edit_first_amount.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_edit_first_amount = QDoubleSpinBox(self.groupBox_edit_first_amount)
        self.doubleSpinBox_edit_first_amount.setObjectName(u"doubleSpinBox_edit_first_amount")
        self.doubleSpinBox_edit_first_amount.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_edit_first_amount.setMaximum(99999.000000000000000)
        self.doubleSpinBox_edit_first_amount.setValue(10.000000000000000)

        self.horizontalLayout_edit_first_amount.addWidget(self.doubleSpinBox_edit_first_amount)

        self.horizontalSpacer_edit_first_amount = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_edit_first_amount.addItem(self.horizontalSpacer_edit_first_amount)


        self.verticalLayout_parameters_content.addWidget(self.groupBox_edit_first_amount)

        self.groupBox_edit_drop_percent = QGroupBox(self.scrollAreaWidgetContents_parameters)
        self.groupBox_edit_drop_percent.setObjectName(u"groupBox_edit_drop_percent")
        self.horizontalLayout_edit_drop_percent = QHBoxLayout(self.groupBox_edit_drop_percent)
        self.horizontalLayout_edit_drop_percent.setSpacing(6)
        self.horizontalLayout_edit_drop_percent.setObjectName(u"horizontalLayout_edit_drop_percent")
        self.horizontalLayout_edit_drop_percent.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_edit_drop_percent = QDoubleSpinBox(self.groupBox_edit_drop_percent)
        self.doubleSpinBox_edit_drop_percent.setObjectName(u"doubleSpinBox_edit_drop_percent")
        self.doubleSpinBox_edit_drop_percent.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_edit_drop_percent.setMaximum(100.000000000000000)
        self.doubleSpinBox_edit_drop_percent.setValue(1.000000000000000)

        self.horizontalLayout_edit_drop_percent.addWidget(self.doubleSpinBox_edit_drop_percent)

        self.horizontalSpacer_edit_drop_percent = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_edit_drop_percent.addItem(self.horizontalSpacer_edit_drop_percent)


        self.verticalLayout_parameters_content.addWidget(self.groupBox_edit_drop_percent)

        self.groupBox_edit_position_multiplier = QGroupBox(self.scrollAreaWidgetContents_parameters)
        self.groupBox_edit_position_multiplier.setObjectName(u"groupBox_edit_position_multiplier")
        self.horizontalLayout_edit_position_multiplier = QHBoxLayout(self.groupBox_edit_position_multiplier)
        self.horizontalLayout_edit_position_multiplier.setSpacing(6)
        self.horizontalLayout_edit_position_multiplier.setObjectName(u"horizontalLayout_edit_position_multiplier")
        self.horizontalLayout_edit_position_multiplier.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_edit_position_multiplier = QDoubleSpinBox(self.groupBox_edit_position_multiplier)
        self.doubleSpinBox_edit_position_multiplier.setObjectName(u"doubleSpinBox_edit_position_multiplier")
        self.doubleSpinBox_edit_position_multiplier.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_edit_position_multiplier.setMinimum(1.000000000000000)
        self.doubleSpinBox_edit_position_multiplier.setMaximum(10.000000000000000)
        self.doubleSpinBox_edit_position_multiplier.setValue(1.300000000000000)

        self.horizontalLayout_edit_position_multiplier.addWidget(self.doubleSpinBox_edit_position_multiplier)

        self.horizontalSpacer_edit_position_multiplier = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_edit_position_multiplier.addItem(self.horizontalSpacer_edit_position_multiplier)


        self.verticalLayout_parameters_content.addWidget(self.groupBox_edit_position_multiplier)

        self.groupBox_edit_profit_percent = QGroupBox(self.scrollAreaWidgetContents_parameters)
        self.groupBox_edit_profit_percent.setObjectName(u"groupBox_edit_profit_percent")
        self.horizontalLayout_edit_profit_percent = QHBoxLayout(self.groupBox_edit_profit_percent)
        self.horizontalLayout_edit_profit_percent.setSpacing(6)
        self.horizontalLayout_edit_profit_percent.setObjectName(u"horizontalLayout_edit_profit_percent")
        self.horizontalLayout_edit_profit_percent.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_edit_profit_percent = QDoubleSpinBox(self.groupBox_edit_profit_percent)
        self.doubleSpinBox_edit_profit_percent.setObjectName(u"doubleSpinBox_edit_profit_percent")
        self.doubleSpinBox_edit_profit_percent.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_edit_profit_percent.setMaximum(100.000000000000000)
        self.doubleSpinBox_edit_profit_percent.setValue(1.500000000000000)

        self.horizontalLayout_edit_profit_percent.addWidget(self.doubleSpinBox_edit_profit_percent)

        self.horizontalSpacer_edit_profit_percent = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_edit_profit_percent.addItem(self.horizontalSpacer_edit_profit_percent)


        self.verticalLayout_parameters_content.addWidget(self.groupBox_edit_profit_percent)

        self.groupBox_edit_max_orders = QGroupBox(self.scrollAreaWidgetContents_parameters)
        self.groupBox_edit_max_orders.setObjectName(u"groupBox_edit_max_orders")
        self.horizontalLayout_edit_max_orders = QHBoxLayout(self.groupBox_edit_max_orders)
        self.horizontalLayout_edit_max_orders.setSpacing(6)
        self.horizontalLayout_edit_max_orders.setObjectName(u"horizontalLayout_edit_max_orders")
        self.horizontalLayout_edit_max_orders.setContentsMargins(6, 4, 6, 4)
        self.spinBox_edit_max_orders = QSpinBox(self.groupBox_edit_max_orders)
        self.spinBox_edit_max_orders.setObjectName(u"spinBox_edit_max_orders")
        self.spinBox_edit_max_orders.setMinimumSize(QSize(120, 0))
        self.spinBox_edit_max_orders.setMinimum(1)
        self.spinBox_edit_max_orders.setMaximum(100)
        self.spinBox_edit_max_orders.setValue(10)

        self.horizontalLayout_edit_max_orders.addWidget(self.spinBox_edit_max_orders)

        self.horizontalSpacer_edit_max_orders = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_edit_max_orders.addItem(self.horizontalSpacer_edit_max_orders)


        self.verticalLayout_parameters_content.addWidget(self.groupBox_edit_max_orders)

        self.groupBox_edit_profit_stop = QGroupBox(self.scrollAreaWidgetContents_parameters)
        self.groupBox_edit_profit_stop.setObjectName(u"groupBox_edit_profit_stop")
        self.horizontalLayout_edit_profit_stop = QHBoxLayout(self.groupBox_edit_profit_stop)
        self.horizontalLayout_edit_profit_stop.setSpacing(6)
        self.horizontalLayout_edit_profit_stop.setObjectName(u"horizontalLayout_edit_profit_stop")
        self.horizontalLayout_edit_profit_stop.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_edit_profit_stop = QDoubleSpinBox(self.groupBox_edit_profit_stop)
        self.doubleSpinBox_edit_profit_stop.setObjectName(u"doubleSpinBox_edit_profit_stop")
        self.doubleSpinBox_edit_profit_stop.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_edit_profit_stop.setMaximum(99999.000000000000000)
        self.doubleSpinBox_edit_profit_stop.setValue(100.000000000000000)

        self.horizontalLayout_edit_profit_stop.addWidget(self.doubleSpinBox_edit_profit_stop)

        self.horizontalSpacer_edit_profit_stop = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_edit_profit_stop.addItem(self.horizontalSpacer_edit_profit_stop)


        self.verticalLayout_parameters_content.addWidget(self.groupBox_edit_profit_stop)

        self.groupBox_edit_dynamic_position = QGroupBox(self.scrollAreaWidgetContents_parameters)
        self.groupBox_edit_dynamic_position.setObjectName(u"groupBox_edit_dynamic_position")
        self.horizontalLayout_edit_dynamic_position = QHBoxLayout(self.groupBox_edit_dynamic_position)
        self.horizontalLayout_edit_dynamic_position.setSpacing(6)
        self.horizontalLayout_edit_dynamic_position.setObjectName(u"horizontalLayout_edit_dynamic_position")
        self.horizontalLayout_edit_dynamic_position.setContentsMargins(6, 4, 6, 4)
        self.checkBox_edit_dynamic_position = QCheckBox(self.groupBox_edit_dynamic_position)
        self.checkBox_edit_dynamic_position.setObjectName(u"checkBox_edit_dynamic_position")
        self.checkBox_edit_dynamic_position.setChecked(True)

        self.horizontalLayout_edit_dynamic_position.addWidget(self.checkBox_edit_dynamic_position)

        self.horizontalSpacer_edit_dynamic_position = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_edit_dynamic_position.addItem(self.horizontalSpacer_edit_dynamic_position)


        self.verticalLayout_parameters_content.addWidget(self.groupBox_edit_dynamic_position)

        self.groupBox_edit_rebound_percent = QGroupBox(self.scrollAreaWidgetContents_parameters)
        self.groupBox_edit_rebound_percent.setObjectName(u"groupBox_edit_rebound_percent")
        self.horizontalLayout_edit_rebound_percent = QHBoxLayout(self.groupBox_edit_rebound_percent)
        self.horizontalLayout_edit_rebound_percent.setSpacing(6)
        self.horizontalLayout_edit_rebound_percent.setObjectName(u"horizontalLayout_edit_rebound_percent")
        self.horizontalLayout_edit_rebound_percent.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_edit_rebound_percent = QDoubleSpinBox(self.groupBox_edit_rebound_percent)
        self.doubleSpinBox_edit_rebound_percent.setObjectName(u"doubleSpinBox_edit_rebound_percent")
        self.doubleSpinBox_edit_rebound_percent.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_edit_rebound_percent.setMaximum(100.000000000000000)
        self.doubleSpinBox_edit_rebound_percent.setValue(0.300000000000000)

        self.horizontalLayout_edit_rebound_percent.addWidget(self.doubleSpinBox_edit_rebound_percent)

        self.horizontalSpacer_edit_rebound_percent = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_edit_rebound_percent.addItem(self.horizontalSpacer_edit_rebound_percent)


        self.verticalLayout_parameters_content.addWidget(self.groupBox_edit_rebound_percent)

        self.scrollArea_parameters.setWidget(self.scrollAreaWidgetContents_parameters)

        self.verticalLayout_edit_parameters.addWidget(self.scrollArea_parameters)

        self.stackedWidget_edit_content.addWidget(self.page_edit_parameters)
        self.page_change_strategy = QWidget()
        self.page_change_strategy.setObjectName(u"page_change_strategy")
        self.verticalLayout_change_strategy = QVBoxLayout(self.page_change_strategy)
        self.verticalLayout_change_strategy.setSpacing(8)
        self.verticalLayout_change_strategy.setObjectName(u"verticalLayout_change_strategy")
        self.groupBox_change_strategy_type = QGroupBox(self.page_change_strategy)
        self.groupBox_change_strategy_type.setObjectName(u"groupBox_change_strategy_type")
        self.horizontalLayout_change_strategy_type = QHBoxLayout(self.groupBox_change_strategy_type)
        self.horizontalLayout_change_strategy_type.setSpacing(20)
        self.horizontalLayout_change_strategy_type.setObjectName(u"horizontalLayout_change_strategy_type")
        self.horizontalLayout_change_strategy_type.setContentsMargins(10, 10, 10, 10)
        self.radioButton_change_aggressive = QRadioButton(self.groupBox_change_strategy_type)
        self.radioButton_change_aggressive.setObjectName(u"radioButton_change_aggressive")
        self.radioButton_change_aggressive.setChecked(True)

        self.horizontalLayout_change_strategy_type.addWidget(self.radioButton_change_aggressive)

        self.radioButton_change_defensive = QRadioButton(self.groupBox_change_strategy_type)
        self.radioButton_change_defensive.setObjectName(u"radioButton_change_defensive")

        self.horizontalLayout_change_strategy_type.addWidget(self.radioButton_change_defensive)

        self.radioButton_change_universal = QRadioButton(self.groupBox_change_strategy_type)
        self.radioButton_change_universal.setObjectName(u"radioButton_change_universal")

        self.horizontalLayout_change_strategy_type.addWidget(self.radioButton_change_universal)

        self.horizontalSpacer_change_strategy_type = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_change_strategy_type.addItem(self.horizontalSpacer_change_strategy_type)


        self.verticalLayout_change_strategy.addWidget(self.groupBox_change_strategy_type)

        self.groupBox_change_sub_strategy = QGroupBox(self.page_change_strategy)
        self.groupBox_change_sub_strategy.setObjectName(u"groupBox_change_sub_strategy")
        self.verticalLayout_change_sub_strategy = QVBoxLayout(self.groupBox_change_sub_strategy)
        self.verticalLayout_change_sub_strategy.setSpacing(6)
        self.verticalLayout_change_sub_strategy.setObjectName(u"verticalLayout_change_sub_strategy")
        self.verticalLayout_change_sub_strategy.setContentsMargins(10, 10, 10, 10)
        self.horizontalLayout_change_sub_strategies = QHBoxLayout()
        self.horizontalLayout_change_sub_strategies.setSpacing(15)
        self.horizontalLayout_change_sub_strategies.setObjectName(u"horizontalLayout_change_sub_strategies")
        self.radioButton_change_universal_a = QRadioButton(self.groupBox_change_sub_strategy)
        self.radioButton_change_universal_a.setObjectName(u"radioButton_change_universal_a")
        self.radioButton_change_universal_a.setVisible(False)
        self.radioButton_change_universal_a.setChecked(False)

        self.horizontalLayout_change_sub_strategies.addWidget(self.radioButton_change_universal_a)

        self.radioButton_change_crazy = QRadioButton(self.groupBox_change_sub_strategy)
        self.radioButton_change_crazy.setObjectName(u"radioButton_change_crazy")
        self.radioButton_change_crazy.setChecked(True)

        self.horizontalLayout_change_sub_strategies.addWidget(self.radioButton_change_crazy)

        self.radioButton_change_aggressive_sub = QRadioButton(self.groupBox_change_sub_strategy)
        self.radioButton_change_aggressive_sub.setObjectName(u"radioButton_change_aggressive_sub")

        self.horizontalLayout_change_sub_strategies.addWidget(self.radioButton_change_aggressive_sub)

        self.radioButton_change_balanced = QRadioButton(self.groupBox_change_sub_strategy)
        self.radioButton_change_balanced.setObjectName(u"radioButton_change_balanced")

        self.horizontalLayout_change_sub_strategies.addWidget(self.radioButton_change_balanced)

        self.radioButton_change_stable = QRadioButton(self.groupBox_change_sub_strategy)
        self.radioButton_change_stable.setObjectName(u"radioButton_change_stable")

        self.horizontalLayout_change_sub_strategies.addWidget(self.radioButton_change_stable)

        self.radioButton_change_conservative = QRadioButton(self.groupBox_change_sub_strategy)
        self.radioButton_change_conservative.setObjectName(u"radioButton_change_conservative")

        self.horizontalLayout_change_sub_strategies.addWidget(self.radioButton_change_conservative)

        self.horizontalSpacer_change_sub_strategies = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_change_sub_strategies.addItem(self.horizontalSpacer_change_sub_strategies)


        self.verticalLayout_change_sub_strategy.addLayout(self.horizontalLayout_change_sub_strategies)


        self.verticalLayout_change_strategy.addWidget(self.groupBox_change_sub_strategy)

        self.groupBox_change_capital = QGroupBox(self.page_change_strategy)
        self.groupBox_change_capital.setObjectName(u"groupBox_change_capital")
        self.horizontalLayout_change_capital = QHBoxLayout(self.groupBox_change_capital)
        self.horizontalLayout_change_capital.setSpacing(6)
        self.horizontalLayout_change_capital.setObjectName(u"horizontalLayout_change_capital")
        self.horizontalLayout_change_capital.setContentsMargins(10, 10, 10, 10)
        self.label_change_capital = QLabel(self.groupBox_change_capital)
        self.label_change_capital.setObjectName(u"label_change_capital")

        self.horizontalLayout_change_capital.addWidget(self.label_change_capital)

        self.doubleSpinBox_change_capital = QDoubleSpinBox(self.groupBox_change_capital)
        self.doubleSpinBox_change_capital.setObjectName(u"doubleSpinBox_change_capital")
        self.doubleSpinBox_change_capital.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_change_capital.setMaximum(99999.000000000000000)
        self.doubleSpinBox_change_capital.setValue(100.000000000000000)

        self.horizontalLayout_change_capital.addWidget(self.doubleSpinBox_change_capital)

        self.horizontalSpacer_change_capital = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_change_capital.addItem(self.horizontalSpacer_change_capital)


        self.verticalLayout_change_strategy.addWidget(self.groupBox_change_capital)

        self.verticalSpacer_change_strategy = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_change_strategy.addItem(self.verticalSpacer_change_strategy)

        self.stackedWidget_edit_content.addWidget(self.page_change_strategy)

        self.verticalLayout_main.addWidget(self.stackedWidget_edit_content)

        self.buttonBox = QDialogButtonBox(StrategyEditWindow)
        self.buttonBox.setObjectName(u"buttonBox")
        self.buttonBox.setOrientation(Qt.Orientation.Horizontal)
        self.buttonBox.setStandardButtons(QDialogButtonBox.StandardButton.Cancel|QDialogButtonBox.StandardButton.Save)

        self.verticalLayout_main.addWidget(self.buttonBox)


        self.retranslateUi(StrategyEditWindow)
        self.buttonBox.accepted.connect(StrategyEditWindow.accept)
        self.buttonBox.rejected.connect(StrategyEditWindow.reject)

        self.stackedWidget_edit_content.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(StrategyEditWindow)
    # setupUi

    def retranslateUi(self, StrategyEditWindow):
        StrategyEditWindow.setWindowTitle(QCoreApplication.translate("StrategyEditWindow", u"\u4fee\u6539\u7b56\u7565", None))
        self.groupBox_current_strategy_info.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u5f53\u524d\u7b56\u7565\u4fe1\u606f", None))
        self.label_trading_pair.setText(QCoreApplication.translate("StrategyEditWindow", u"\u4ea4\u6613\u5bf9:", None))
        self.label_trading_pair_value.setText(QCoreApplication.translate("StrategyEditWindow", u"BTC/USDT", None))
        self.label_strategy_type.setText(QCoreApplication.translate("StrategyEditWindow", u"\u7b56\u7565\u7c7b\u578b:", None))
        self.label_strategy_type_value.setText(QCoreApplication.translate("StrategyEditWindow", u"\u81ea\u5b9a\u4e49\u7b56\u7565", None))
        self.label_create_time.setText(QCoreApplication.translate("StrategyEditWindow", u"\u521b\u5efa\u65f6\u95f4:", None))
        self.label_create_time_value.setText(QCoreApplication.translate("StrategyEditWindow", u"2024-01-01 10:00:00", None))
        self.label_current_status.setText(QCoreApplication.translate("StrategyEditWindow", u"\u5f53\u524d\u72b6\u6001:", None))
        self.label_current_status_value.setStyleSheet(QCoreApplication.translate("StrategyEditWindow", u"color: green; font-weight: bold;", None))
        self.label_current_status_value.setText(QCoreApplication.translate("StrategyEditWindow", u"\u8fd0\u884c\u4e2d", None))
        self.groupBox_edit_options.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u4fee\u6539\u9009\u9879", None))
        self.radioButton_edit_parameters.setText(QCoreApplication.translate("StrategyEditWindow", u"\u4fee\u6539\u81ea\u5b9a\u4e49\u7b56\u7565", None))
        self.radioButton_change_strategy.setText(QCoreApplication.translate("StrategyEditWindow", u"\u4fee\u6539AI\u7b56\u7565", None))
        self.groupBox_edit_first_amount.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u9996\u5355\u91d1\u989d", None))
        self.doubleSpinBox_edit_first_amount.setSuffix(QCoreApplication.translate("StrategyEditWindow", u" U", None))
        self.groupBox_edit_drop_percent.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u8dcc\u5e45\u591a\u5c11\u52a0\u4ed3", None))
        self.doubleSpinBox_edit_drop_percent.setSuffix(QCoreApplication.translate("StrategyEditWindow", u" %", None))
        self.groupBox_edit_position_multiplier.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u52a0\u4ed3\u500d\u6570", None))
        self.doubleSpinBox_edit_position_multiplier.setSuffix(QCoreApplication.translate("StrategyEditWindow", u" \u500d", None))
        self.groupBox_edit_profit_percent.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u767e\u5206\u6bd4\u6b62\u76c8\u6bd4\u4f8b", None))
        self.doubleSpinBox_edit_profit_percent.setSuffix(QCoreApplication.translate("StrategyEditWindow", u" %", None))
        self.groupBox_edit_max_orders.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u6700\u5927\u6301\u4ed3\u5355\u6570", None))
        self.spinBox_edit_max_orders.setSuffix(QCoreApplication.translate("StrategyEditWindow", u" \u6b21", None))
        self.groupBox_edit_profit_stop.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u76c8\u5229\u591a\u5c11\u505c\u6b62\u673a\u5668\u4eba", None))
        self.doubleSpinBox_edit_profit_stop.setSuffix(QCoreApplication.translate("StrategyEditWindow", u" U", None))
        self.groupBox_edit_dynamic_position.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u52a8\u6001\u6301\u4ed3", None))
        self.checkBox_edit_dynamic_position.setText(QCoreApplication.translate("StrategyEditWindow", u"\u5f00\u542f\u52a8\u6001\u6301\u4ed3", None))
        self.groupBox_edit_rebound_percent.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u767e\u5206\u6bd4\u53cd\u5f39\u591a\u5c11\u8865\u4ed3", None))
        self.doubleSpinBox_edit_rebound_percent.setSuffix(QCoreApplication.translate("StrategyEditWindow", u" %", None))
        self.groupBox_change_strategy_type.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u7b56\u7565\u7c7b\u578b\u9009\u62e9", None))
        self.radioButton_change_aggressive.setText(QCoreApplication.translate("StrategyEditWindow", u"\u8fdb\u653b\u578b\u7b56\u7565", None))
        self.radioButton_change_defensive.setText(QCoreApplication.translate("StrategyEditWindow", u"\u9632\u5b88\u578b\u7b56\u7565", None))
        self.radioButton_change_universal.setText(QCoreApplication.translate("StrategyEditWindow", u"\u901a\u7528\u578b\u7b56\u7565", None))
        self.groupBox_change_sub_strategy.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u5b50\u7b56\u7565\u9009\u62e9", None))
        self.radioButton_change_universal_a.setText(QCoreApplication.translate("StrategyEditWindow", u"A", None))
        self.radioButton_change_crazy.setText(QCoreApplication.translate("StrategyEditWindow", u"\u75af\u72c2\u578b", None))
        self.radioButton_change_aggressive_sub.setText(QCoreApplication.translate("StrategyEditWindow", u"\u6fc0\u8fdb\u578b", None))
        self.radioButton_change_balanced.setText(QCoreApplication.translate("StrategyEditWindow", u"\u5e73\u8861\u578b", None))
        self.radioButton_change_stable.setText(QCoreApplication.translate("StrategyEditWindow", u"\u7a33\u5065\u578b", None))
        self.radioButton_change_conservative.setText(QCoreApplication.translate("StrategyEditWindow", u"\u4fdd\u5b88\u578b", None))
        self.groupBox_change_capital.setTitle(QCoreApplication.translate("StrategyEditWindow", u"\u6295\u5165\u672c\u91d1\u8bbe\u7f6e", None))
        self.label_change_capital.setText(QCoreApplication.translate("StrategyEditWindow", u"\u6295\u5165\u672c\u91d1:", None))
        self.doubleSpinBox_change_capital.setSuffix(QCoreApplication.translate("StrategyEditWindow", u" U", None))
    # retranslateUi

