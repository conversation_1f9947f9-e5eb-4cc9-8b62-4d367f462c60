# -*- coding: utf-8 -*-
"""
日志服务类
负责统一管理系统日志功能
"""

import os
import logging
from logging.handlers import TimedRotatingFileHandler
from typing import Optional


class 类_日志服务:
    """
    日志服务类
    提供统一的日志管理功能
    """
    
    def __init__(self, 参_日志级别: int = logging.INFO):
        """
        初始化日志服务
        
        @param 参_日志级别 (int): 日志级别，默认INFO
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 设置基本属性
        self.类_模块名 = "日志服务"
        self.日志级别 = 参_日志级别
        
        # 步骤2: 配置日志系统
        self.配置日志系统()
        
        # 步骤3: 获取日志记录器
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("日志服务初始化完成")
        print("日志服务初始化完成")
    
    def 配置日志系统(self) -> None:
        """
        配置标准Python日志系统
        
        @return None
        @exception Exception: 配置过程中可能发生的未知错误
        """
        try:
            # 步骤1: 创建logs目录
            # 确保日志目录存在
            日志目录 = "logs"
            if not os.path.exists(日志目录):
                os.makedirs(日志目录)
            
            # 步骤2: 配置根日志记录器
            # 获取根日志记录器并设置级别
            根日志记录器 = logging.getLogger()
            根日志记录器.setLevel(self.日志级别)
            
            # 步骤3: 清除现有的处理器
            # 避免重复添加处理器
            for 处理器 in 根日志记录器.handlers[:]:
                根日志记录器.removeHandler(处理器)
            
            # 步骤4: 创建格式化器
            # 定义日志输出格式
            格式化器 = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            
            # 步骤5: 添加按日期轮转的文件处理器
            # 每天轮转，保留30天的日志
            日志文件 = os.path.join(日志目录, "系统日志.log")
            日期处理器 = TimedRotatingFileHandler(
                日志文件 + '.daily',
                when='midnight',
                interval=1,
                backupCount=30,
                encoding='utf-8'
            )
            日期处理器.setFormatter(格式化器)
            根日志记录器.addHandler(日期处理器)
            
            # 步骤6: 添加控制台处理器
            # 同时输出到控制台
            控制台处理器 = logging.StreamHandler()
            控制台处理器.setFormatter(格式化器)
            根日志记录器.addHandler(控制台处理器)
            
            print("日志系统配置完成")
            
        except Exception as e:
            print(f"配置日志系统失败: {e}")
            raise
    
    def 获取日志记录器(self, 参_模块名: Optional[str] = None) -> logging.Logger:
        """
        获取指定模块的日志记录器
        
        @param 参_模块名 (Optional[str]): 模块名称，如果为None则使用调用者的模块名
        @return logging.Logger: 日志记录器实例
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            if 参_模块名:
                return logging.getLogger(参_模块名)
            else:
                return logging.getLogger()
        except Exception as e:
            print(f"获取日志记录器失败: {e}")
            raise
    
    def 设置日志级别(self, 参_日志级别: int) -> None:
        """
        设置日志级别
        
        @param 参_日志级别 (int): 新的日志级别
        @return None
        @exception Exception: 设置过程中可能发生的未知错误
        """
        try:
            self.日志级别 = 参_日志级别
            根日志记录器 = logging.getLogger()
            根日志记录器.setLevel(参_日志级别)
            
            self.logger.info(f"日志级别已设置为: {参_日志级别}")
            
        except Exception as e:
            self.logger.error(f"设置日志级别失败: {e}", exc_info=True)
            raise
    
    def 记录信息(self, 参_消息: str, 参_模块名: Optional[str] = None) -> None:
        """
        记录信息级别日志
        
        @param 参_消息 (str): 日志消息
        @param 参_模块名 (Optional[str]): 模块名称
        @return None
        @exception Exception: 记录过程中可能发生的未知错误
        """
        try:
            日志记录器 = self.获取日志记录器(参_模块名)
            日志记录器.info(参_消息)
        except Exception as e:
            print(f"记录信息日志失败: {e}")
    
    def 记录警告(self, 参_消息: str, 参_模块名: Optional[str] = None) -> None:
        """
        记录警告级别日志
        
        @param 参_消息 (str): 日志消息
        @param 参_模块名 (Optional[str]): 模块名称
        @return None
        @exception Exception: 记录过程中可能发生的未知错误
        """
        try:
            日志记录器 = self.获取日志记录器(参_模块名)
            日志记录器.warning(参_消息)
        except Exception as e:
            print(f"记录警告日志失败: {e}")
    
    def 记录错误(self, 参_消息: str, 参_模块名: Optional[str] = None, 参_异常信息: bool = False) -> None:
        """
        记录错误级别日志
        
        @param 参_消息 (str): 日志消息
        @param 参_模块名 (Optional[str]): 模块名称
        @param 参_异常信息 (bool): 是否包含异常堆栈信息
        @return None
        @exception Exception: 记录过程中可能发生的未知错误
        """
        try:
            日志记录器 = self.获取日志记录器(参_模块名)
            日志记录器.error(参_消息, exc_info=参_异常信息)
        except Exception as e:
            print(f"记录错误日志失败: {e}")
    
    def 记录调试(self, 参_消息: str, 参_模块名: Optional[str] = None) -> None:
        """
        记录调试级别日志
        
        @param 参_消息 (str): 日志消息
        @param 参_模块名 (Optional[str]): 模块名称
        @return None
        @exception Exception: 记录过程中可能发生的未知错误
        """
        try:
            日志记录器 = self.获取日志记录器(参_模块名)
            日志记录器.debug(参_消息)
        except Exception as e:
            print(f"记录调试日志失败: {e}")
    
    def 获取日志级别(self) -> int:
        """
        获取当前日志级别
        
        @return int: 当前日志级别
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            return self.日志级别
        except Exception as e:
            print(f"获取日志级别失败: {e}")
            return logging.INFO
