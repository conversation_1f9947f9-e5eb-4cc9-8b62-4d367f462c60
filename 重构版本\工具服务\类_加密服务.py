# -*- coding: utf-8 -*-
"""
加密服务类
负责统一管理数据加密解密功能，封装原有的加密工具类
"""

import logging
import sys
import os
from typing import Optional

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)


class 类_加密服务:
    """
    加密服务类
    封装原有的加密工具功能，提供服务化接口
    """
    
    def __init__(self):
        """
        初始化加密服务
        
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 设置基本属性
        self.类_模块名 = "加密服务"
        
        # 步骤2: 初始化加密工具
        self._加密工具 = None
        self.初始化加密工具()
        
        logger.info("加密服务初始化完成")
        print("加密服务初始化完成")
    
    def 初始化加密工具(self) -> None:
        """
        初始化加密工具
        
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        try:
            # 步骤1: 添加项目根目录到sys.path
            当前目录 = os.path.dirname(os.path.abspath(__file__))
            项目根目录 = os.path.dirname(os.path.dirname(当前目录))
            if 项目根目录 not in sys.path:
                sys.path.insert(0, 项目根目录)
            
            # 步骤2: 导入原有的加密工具类
            from 模块类.功能类.类_加密工具 import 类_加密工具
            
            # 步骤3: 创建加密工具实例
            self._加密工具 = 类_加密工具()
            
            logger.info("加密工具初始化完成")
            
        except ImportError as e:
            logger.error(f"导入加密工具类失败: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"初始化加密工具失败: {e}", exc_info=True)
            raise
    
    def 获取加密工具(self):
        """
        获取原有的加密工具实例
        
        @return: 加密工具实例
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            if self._加密工具 is None:
                self.初始化加密工具()
            return self._加密工具
        except Exception as e:
            logger.error(f"获取加密工具失败: {e}", exc_info=True)
            raise
    
    def 加密数据(self, 参_明文: str) -> str:
        """
        加密数据
        
        @param 参_明文 (str): 要加密的明文字符串
        @return str: 加密后的字符串（base64编码）
        @exception Exception: 加密过程中可能发生的未知错误
        """
        try:
            if not 参_明文:
                logger.warning("明文为空，返回空字符串")
                return ""
            
            加密工具 = self.获取加密工具()
            加密结果 = 加密工具.加密(参_明文)
            
            if 加密结果:
                logger.debug("数据加密成功")
            else:
                logger.error("数据加密失败")
            
            return 加密结果
            
        except Exception as e:
            logger.error(f"加密数据失败: {e}", exc_info=True)
            return ""
    
    def 解密数据(self, 参_密文: str) -> str:
        """
        解密数据
        
        @param 参_密文 (str): 要解密的密文字符串（base64编码）
        @return str: 解密后的明文字符串
        @exception Exception: 解密过程中可能发生的未知错误
        """
        try:
            if not 参_密文:
                logger.warning("密文为空，返回空字符串")
                return ""
            
            加密工具 = self.获取加密工具()
            解密结果 = 加密工具.解密(参_密文)
            
            if 解密结果:
                logger.debug("数据解密成功")
            else:
                logger.error("数据解密失败")
            
            return 解密结果
            
        except Exception as e:
            logger.error(f"解密数据失败: {e}", exc_info=True)
            return ""
    
    def 加密API密钥(self, 参_API密钥: str) -> str:
        """
        加密API密钥
        专门用于加密API密钥的便捷方法
        
        @param 参_API密钥 (str): 要加密的API密钥
        @return str: 加密后的API密钥
        @exception Exception: 加密过程中可能发生的未知错误
        """
        try:
            if not 参_API密钥:
                logger.warning("API密钥为空")
                return ""
            
            加密结果 = self.加密数据(参_API密钥)
            
            if 加密结果:
                logger.info("API密钥加密成功")
            else:
                logger.error("API密钥加密失败")
            
            return 加密结果
            
        except Exception as e:
            logger.error(f"加密API密钥失败: {e}", exc_info=True)
            return ""
    
    def 解密API密钥(self, 参_加密API密钥: str) -> str:
        """
        解密API密钥
        专门用于解密API密钥的便捷方法
        
        @param 参_加密API密钥 (str): 要解密的加密API密钥
        @return str: 解密后的API密钥
        @exception Exception: 解密过程中可能发生的未知错误
        """
        try:
            if not 参_加密API密钥:
                logger.warning("加密API密钥为空")
                return ""
            
            解密结果 = self.解密数据(参_加密API密钥)
            
            if 解密结果:
                logger.info("API密钥解密成功")
            else:
                logger.error("API密钥解密失败")
            
            return 解密结果
            
        except Exception as e:
            logger.error(f"解密API密钥失败: {e}", exc_info=True)
            return ""
    
    def 验证加密功能(self) -> bool:
        """
        验证加密功能是否正常
        
        @return bool: 加密功能是否正常
        @exception Exception: 验证过程中可能发生的未知错误
        """
        try:
            # 步骤1: 准备测试数据
            测试明文 = "测试加密功能"
            
            # 步骤2: 执行加密
            加密结果 = self.加密数据(测试明文)
            if not 加密结果:
                logger.error("加密测试失败")
                return False
            
            # 步骤3: 执行解密
            解密结果 = self.解密数据(加密结果)
            if not 解密结果:
                logger.error("解密测试失败")
                return False
            
            # 步骤4: 验证结果
            if 解密结果 == 测试明文:
                logger.info("加密功能验证成功")
                return True
            else:
                logger.error(f"加密功能验证失败: 期望'{测试明文}', 实际'{解密结果}'")
                return False
                
        except Exception as e:
            logger.error(f"验证加密功能失败: {e}", exc_info=True)
            return False
    
    def 是否可用(self) -> bool:
        """
        检查加密服务是否可用
        
        @return bool: 加密服务是否可用
        @exception Exception: 检查过程中可能发生的未知错误
        """
        try:
            return self._加密工具 is not None
        except Exception as e:
            logger.error(f"检查加密服务可用性失败: {e}", exc_info=True)
            return False
