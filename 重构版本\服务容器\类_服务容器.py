# -*- coding: utf-8 -*-
"""
服务容器类
负责管理所有服务的生命周期和依赖注入
"""

import logging
from typing import Dict, Any, Optional, Type, Callable
import threading

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)


class 类_服务容器:
    """
    服务容器类
    使用单例模式，管理所有服务的注册、创建和获取
    """
    
    _实例 = None
    _锁 = threading.Lock()
    
    def __new__(cls):
        """
        单例模式实现
        确保整个应用程序只有一个服务容器实例
        
        @return 类_服务容器: 服务容器实例
        @exception Exception: 创建实例过程中可能发生的未知错误
        """
        if cls._实例 is None:
            with cls._锁:
                if cls._实例 is None:
                    cls._实例 = super().__new__(cls)
        return cls._实例
    
    def __init__(self):
        """
        初始化服务容器
        
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 检查是否已经初始化
        # 避免单例模式下重复初始化
        if hasattr(self, '_已初始化'):
            return
        
        # 步骤2: 初始化服务注册表
        # 存储服务名称到服务工厂函数的映射
        self._服务注册表: Dict[str, Dict[str, Any]] = {}
        
        # 步骤3: 初始化服务实例缓存
        # 存储已创建的服务实例，实现单例服务
        self._服务实例缓存: Dict[str, Any] = {}
        
        # 步骤4: 初始化线程锁
        # 保护服务注册表和实例缓存的线程安全
        self._服务锁 = threading.Lock()
        
        # 步骤5: 设置模块名称
        self.类_模块名 = "服务容器"
        
        # 步骤6: 标记已初始化
        self._已初始化 = True
        
        logger.info("服务容器初始化完成")
        print("服务容器初始化完成")
    
    def 注册服务(self, 参_服务名称: str, 参_服务工厂: Callable, 参_是否单例: bool = True, 参_依赖列表: Optional[list] = None) -> None:
        """
        注册服务到容器中
        
        @param 参_服务名称 (str): 服务的唯一名称
        @param 参_服务工厂 (Callable): 服务的工厂函数或类
        @param 参_是否单例 (bool): 是否为单例服务，默认True
        @param 参_依赖列表 (Optional[list]): 服务依赖的其他服务名称列表
        @return None
        @exception ValueError: 当服务名称为空或已存在时抛出
        @exception Exception: 注册过程中可能发生的未知错误
        """
        try:
            # 步骤1: 验证输入参数
            # 检查服务名称是否有效
            if not 参_服务名称:
                raise ValueError("服务名称不能为空")
            
            if not 参_服务工厂:
                raise ValueError("服务工厂不能为空")
            
            # 步骤2: 检查服务是否已注册
            # 避免重复注册同名服务
            with self._服务锁:
                if 参_服务名称 in self._服务注册表:
                    logger.warning(f"服务 '{参_服务名称}' 已存在，将被覆盖")
                
                # 步骤3: 注册服务信息
                # 将服务信息存储到注册表中
                self._服务注册表[参_服务名称] = {
                    '工厂函数': 参_服务工厂,
                    '是否单例': 参_是否单例,
                    '依赖列表': 参_依赖列表 or [],
                    '已创建': False
                }
            
            logger.info(f"服务 '{参_服务名称}' 注册成功，单例: {参_是否单例}")
            
        except Exception as e:
            logger.error(f"注册服务 '{参_服务名称}' 失败: {e}", exc_info=True)
            raise
    
    def 获取服务(self, 参_服务名称: str) -> Any:
        """
        从容器中获取服务实例
        
        @param 参_服务名称 (str): 服务名称
        @return Any: 服务实例
        @exception ValueError: 当服务未注册时抛出
        @exception Exception: 创建服务过程中可能发生的未知错误
        """
        try:
            # 步骤1: 检查服务是否已注册
            # 确保请求的服务已经在容器中注册
            if 参_服务名称 not in self._服务注册表:
                raise ValueError(f"服务 '{参_服务名称}' 未注册")
            
            # 步骤2: 获取服务信息
            # 从注册表中获取服务的详细信息
            服务信息 = self._服务注册表[参_服务名称]
            
            # 步骤3: 检查是否为单例服务
            # 如果是单例服务且已创建，直接返回缓存的实例
            if 服务信息['是否单例']:
                with self._服务锁:
                    if 参_服务名称 in self._服务实例缓存:
                        return self._服务实例缓存[参_服务名称]
            
            # 步骤4: 创建服务实例
            # 解析依赖并创建新的服务实例
            服务实例 = self._创建服务实例(参_服务名称, 服务信息)
            
            # 步骤5: 缓存单例服务
            # 如果是单例服务，将实例存储到缓存中
            if 服务信息['是否单例']:
                with self._服务锁:
                    self._服务实例缓存[参_服务名称] = 服务实例
            
            logger.debug(f"服务 '{参_服务名称}' 获取成功")
            return 服务实例
            
        except Exception as e:
            logger.error(f"获取服务 '{参_服务名称}' 失败: {e}", exc_info=True)
            raise
    
    def _创建服务实例(self, 参_服务名称: str, 参_服务信息: Dict[str, Any]) -> Any:
        """
        创建服务实例的内部方法
        
        @param 参_服务名称 (str): 服务名称
        @param 参_服务信息 (Dict[str, Any]): 服务信息
        @return Any: 创建的服务实例
        @exception Exception: 创建过程中可能发生的未知错误
        """
        try:
            # 步骤1: 解析服务依赖
            # 递归获取服务所依赖的其他服务实例
            依赖实例列表 = []
            for 依赖服务名称 in 参_服务信息['依赖列表']:
                依赖实例 = self.获取服务(依赖服务名称)
                依赖实例列表.append(依赖实例)
            
            # 步骤2: 调用工厂函数创建实例
            # 根据依赖数量选择不同的调用方式
            工厂函数 = 参_服务信息['工厂函数']
            
            if len(依赖实例列表) == 0:
                # 无依赖服务
                服务实例 = 工厂函数()
            elif len(依赖实例列表) == 1:
                # 单个依赖服务
                服务实例 = 工厂函数(依赖实例列表[0])
            else:
                # 多个依赖服务
                服务实例 = 工厂函数(*依赖实例列表)
            
            logger.debug(f"服务实例 '{参_服务名称}' 创建成功")
            return 服务实例
            
        except Exception as e:
            logger.error(f"创建服务实例 '{参_服务名称}' 失败: {e}", exc_info=True)
            raise
    
    def 是否已注册(self, 参_服务名称: str) -> bool:
        """
        检查服务是否已注册
        
        @param 参_服务名称 (str): 服务名称
        @return bool: 是否已注册
        @exception Exception: 检查过程中可能发生的未知错误
        """
        try:
            return 参_服务名称 in self._服务注册表
        except Exception as e:
            logger.error(f"检查服务注册状态失败: {e}", exc_info=True)
            return False
    
    def 获取已注册服务列表(self) -> list:
        """
        获取所有已注册的服务名称列表
        
        @return list: 已注册的服务名称列表
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            return list(self._服务注册表.keys())
        except Exception as e:
            logger.error(f"获取已注册服务列表失败: {e}", exc_info=True)
            return []
    
    def 清空容器(self) -> None:
        """
        清空容器中的所有服务
        主要用于测试和重置
        
        @return None
        @exception Exception: 清空过程中可能发生的未知错误
        """
        try:
            with self._服务锁:
                self._服务注册表.clear()
                self._服务实例缓存.clear()
            
            logger.info("服务容器已清空")
            
        except Exception as e:
            logger.error(f"清空服务容器失败: {e}", exc_info=True)
            raise


# 全局服务容器实例
全_服务容器 = 类_服务容器()
