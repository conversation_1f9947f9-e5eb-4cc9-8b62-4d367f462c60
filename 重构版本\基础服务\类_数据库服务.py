# -*- coding: utf-8 -*-
"""
数据库服务类
负责统一管理数据库相关功能，封装原有的数据库管理类
"""

import logging
import sys
import os
from typing import Optional, Any, Dict, List

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)


class 类_数据库服务:
    """
    数据库服务类
    封装原有的数据库管理功能，提供服务化接口
    """
    
    def __init__(self, 参_配置服务):
        """
        初始化数据库服务
        
        @param 参_配置服务: 配置服务实例
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 设置基本属性
        self.类_模块名 = "数据库服务"
        self.配置服务 = 参_配置服务
        
        # 步骤2: 初始化数据库管理器
        self._数据库管理器 = None
        self.初始化数据库管理器()
        
        logger.info("数据库服务初始化完成")
        print("数据库服务初始化完成")
    
    def 初始化数据库管理器(self) -> None:
        """
        初始化数据库管理器
        
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        try:
            # 步骤1: 添加项目根目录到sys.path
            # 确保能够导入原有的数据库管理类
            当前目录 = os.path.dirname(os.path.abspath(__file__))
            项目根目录 = os.path.dirname(os.path.dirname(当前目录))
            if 项目根目录 not in sys.path:
                sys.path.insert(0, 项目根目录)
            
            # 步骤2: 导入原有的数据库管理类
            from 数据库.数据库管理 import 类_数据库管理
            
            # 步骤3: 获取数据库路径
            数据库路径 = self.配置服务.获取数据库路径()
            
            # 步骤4: 创建数据库管理器实例
            self._数据库管理器 = 类_数据库管理(数据库路径)
            
            logger.info(f"数据库管理器初始化完成，数据库路径: {数据库路径}")
            
        except ImportError as e:
            logger.error(f"导入数据库管理类失败: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"初始化数据库管理器失败: {e}", exc_info=True)
            raise
    
    def 获取数据库管理器(self):
        """
        获取原有的数据库管理器实例
        
        @return: 数据库管理器实例
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            if self._数据库管理器 is None:
                self.初始化数据库管理器()
            return self._数据库管理器
        except Exception as e:
            logger.error(f"获取数据库管理器失败: {e}", exc_info=True)
            raise
    
    def 获取机器人列表(self) -> List[Dict[str, Any]]:
        """
        获取所有机器人列表
        
        @return List[Dict[str, Any]]: 机器人数据列表
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            数据库管理器 = self.获取数据库管理器()
            机器人列表 = 数据库管理器.获取所有机器人()
            
            logger.debug(f"获取机器人列表成功，共 {len(机器人列表)} 个机器人")
            return 机器人列表
            
        except Exception as e:
            logger.error(f"获取机器人列表失败: {e}", exc_info=True)
            return []
    
    def 获取机器人数据(self, 参_机器人编号: str) -> Optional[Dict[str, Any]]:
        """
        获取指定机器人的数据
        
        @param 参_机器人编号 (str): 机器人编号
        @return Optional[Dict[str, Any]]: 机器人数据，如果不存在则返回None
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            数据库管理器 = self.获取数据库管理器()
            机器人数据 = 数据库管理器.获取机器人(参_机器人编号)
            
            if 机器人数据:
                logger.debug(f"获取机器人数据成功: {参_机器人编号}")
            else:
                logger.warning(f"机器人不存在: {参_机器人编号}")
            
            return 机器人数据
            
        except Exception as e:
            logger.error(f"获取机器人数据失败: {参_机器人编号}, 错误: {e}", exc_info=True)
            return None
    
    def 保存机器人数据(self, 参_机器人数据: Dict[str, Any]) -> bool:
        """
        保存机器人数据
        
        @param 参_机器人数据 (Dict[str, Any]): 机器人数据
        @return bool: 保存是否成功
        @exception Exception: 保存过程中可能发生的未知错误
        """
        try:
            数据库管理器 = self.获取数据库管理器()
            成功 = 数据库管理器.添加机器人(参_机器人数据)
            
            if 成功:
                机器人编号 = 参_机器人数据.get('机器人编号', '未知')
                logger.info(f"保存机器人数据成功: {机器人编号}")
            else:
                logger.error("保存机器人数据失败")
            
            return 成功
            
        except Exception as e:
            logger.error(f"保存机器人数据失败: {e}", exc_info=True)
            return False
    
    def 更新机器人数据(self, 参_机器人编号: str, 参_更新数据: Dict[str, Any]) -> bool:
        """
        更新机器人数据
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_更新数据 (Dict[str, Any]): 要更新的数据
        @return bool: 更新是否成功
        @exception Exception: 更新过程中可能发生的未知错误
        """
        try:
            数据库管理器 = self.获取数据库管理器()
            成功 = 数据库管理器.更新机器人(参_机器人编号, 参_更新数据)
            
            if 成功:
                logger.info(f"更新机器人数据成功: {参_机器人编号}")
            else:
                logger.error(f"更新机器人数据失败: {参_机器人编号}")
            
            return 成功
            
        except Exception as e:
            logger.error(f"更新机器人数据失败: {参_机器人编号}, 错误: {e}", exc_info=True)
            return False
    
    def 删除机器人数据(self, 参_机器人编号: str) -> bool:
        """
        删除机器人数据
        
        @param 参_机器人编号 (str): 机器人编号
        @return bool: 删除是否成功
        @exception Exception: 删除过程中可能发生的未知错误
        """
        try:
            数据库管理器 = self.获取数据库管理器()
            成功 = 数据库管理器.删除机器人(参_机器人编号)
            
            if 成功:
                logger.info(f"删除机器人数据成功: {参_机器人编号}")
            else:
                logger.error(f"删除机器人数据失败: {参_机器人编号}")
            
            return 成功
            
        except Exception as e:
            logger.error(f"删除机器人数据失败: {参_机器人编号}, 错误: {e}", exc_info=True)
            return False
    
    def 获取系统配置(self, 参_配置键: str) -> Optional[Any]:
        """
        获取系统配置
        
        @param 参_配置键 (str): 配置键名
        @return Optional[Any]: 配置值，如果不存在则返回None
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            数据库管理器 = self.获取数据库管理器()
            配置值 = 数据库管理器.获取系统配置(参_配置键)
            
            logger.debug(f"获取系统配置: {参_配置键} = {配置值}")
            return 配置值
            
        except Exception as e:
            logger.error(f"获取系统配置失败: {参_配置键}, 错误: {e}", exc_info=True)
            return None
    
    def 设置系统配置(self, 参_配置键: str, 参_配置值: Any) -> bool:
        """
        设置系统配置
        
        @param 参_配置键 (str): 配置键名
        @param 参_配置值 (Any): 配置值
        @return bool: 设置是否成功
        @exception Exception: 设置过程中可能发生的未知错误
        """
        try:
            数据库管理器 = self.获取数据库管理器()
            成功 = 数据库管理器.设置系统配置(参_配置键, 参_配置值)
            
            if 成功:
                logger.info(f"设置系统配置成功: {参_配置键} = {参_配置值}")
            else:
                logger.error(f"设置系统配置失败: {参_配置键}")
            
            return 成功
            
        except Exception as e:
            logger.error(f"设置系统配置失败: {参_配置键}, 错误: {e}", exc_info=True)
            return False
    
    def 获取交易对列表(self) -> List[Dict[str, Any]]:
        """
        获取所有交易对列表
        
        @return List[Dict[str, Any]]: 交易对数据列表
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            数据库管理器 = self.获取数据库管理器()
            交易对列表 = 数据库管理器.获取所有交易对()
            
            logger.debug(f"获取交易对列表成功，共 {len(交易对列表)} 个交易对")
            return 交易对列表
            
        except Exception as e:
            logger.error(f"获取交易对列表失败: {e}", exc_info=True)
            return []
    
    def 关闭连接(self) -> None:
        """
        关闭数据库连接
        
        @return None
        @exception Exception: 关闭过程中可能发生的未知错误
        """
        try:
            if self._数据库管理器:
                self._数据库管理器.关闭连接()
                logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}", exc_info=True)
